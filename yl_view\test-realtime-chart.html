<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RealTimeChart 测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0a1929 0%, #1e3a8a 100%);
            font-family: Arial, sans-serif;
            min-height: 100vh;
        }
        
        .container {
            width: 100%;
            height: 80vh;
            background: rgba(0, 20, 50, 0.3);
            border-radius: 12px;
            padding: 20px;
            box-sizing: border-box;
        }
        
        h1 {
            color: #4fc3f7;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
        }
    </style>
</head>
<body>
    <h1>RealTimeChart 组件测试</h1>
    <div class="container">
        <div id="app"></div>
    </div>

    <script type="module">
        import { createApp } from 'vue'
        import RealTimeChart from './src/components/charts/RealTimeChart.vue'

        createApp({
            components: {
                RealTimeChart
            },
            template: '<RealTimeChart />'
        }).mount('#app')
    </script>
</body>
</html>
