<template>
  <div class="district-building-list">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="区内楼宇列表">
          <template #extra>
            <select v-model="selectedStreet" @change="filterBuildings" class="title-filter-select">
              <option value="">全部街道</option>
              <option v-for="street in streets" :key="street" :value="street">{{ street }}</option>
            </select>
            <select v-model="selectedStar" @change="filterBuildings" class="title-filter-select">
              <option value="">全部星级</option>
              <option value="4">4星标准级</option>
              <option value="5">5星甲级</option>
              <option value="6">6星超甲级</option>
              <option value="7">7星顶级</option>
            </select>
          </template>
        </ChartTitle>
        
        <div class="table-container">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <div class="loading-text">正在加载楼宇数据...</div>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-container">
            <div class="error-text">{{ error }}</div>
          </div>

          <!-- 无数据状态 -->
          <div v-else-if="!buildingList || buildingList.length === 0" class="empty-container">
            <div class="empty-text">暂无楼宇数据</div>
          </div>

          <!-- 正常数据显示 -->
          <template v-else>
            <div class="table-header">
              <div class="header-cell">楼宇名称</div>
              <div class="header-cell">街道</div>
              <div class="header-cell">星级</div>
              <div class="header-cell">建筑面积</div>
              <div class="header-cell">空置率</div>
            </div>
          
          <div
            class="table-body"
            ref="tableBodyRef"
            @mouseenter="pauseAutoScroll"
            @mouseleave="resumeAutoScroll"
          >
            <div
              v-for="building in filteredBuildings"
              :key="building.id"
              class="table-row"
              @click="goToBuildingDetail(building)"
              :class="{ 'selected': selectedBuilding && selectedBuilding.id === building.id }"
            >
              <div class="table-cell">{{ building.buildingName }}</div>
              <div class="table-cell">{{ building.streetName }}</div>
              <div class="table-cell">
                <span class="star-badge" :class="getStarClass(building.starLevel)">{{ building.starLevel }}</span>
              </div>
              <div class="table-cell">{{ formatArea(building.buildingArea) }}㎡</div>
              <div class="table-cell">
                <span class="vacancy-rate" :class="getVacancyClass(building.vacancyRate)">
                  {{ formatPercentage(building.vacancyRate) }}%
                </span>
              </div>
              <!-- <div class="table-cell">{{ building.avgRent }}元/㎡</div> -->
            </div>
          </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import ChartTitle from '@/components/common/ChartTitle.vue';

// 路由实例
const router = useRouter();

// 接收父组件传递的数据
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: '长沙市',
      level: 'city',
      areaData: null
    })
  },
  buildingList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  }
});

// 筛选状态
const selectedStreet = ref('');
const selectedStar = ref('');
const selectedBuilding = ref(null);
const tableBodyRef = ref(null);

// 动态街道和楼宇数据
const streets = computed(() => {
  // 从传递的楼宇列表中提取唯一的街道名称
  if (!props.buildingList || props.buildingList.length === 0) {
    return [];
  }
  const streetSet = new Set();
  props.buildingList.forEach(building => {
    if (building.streetName) {
      streetSet.add(building.streetName);
    }
  });

  return Array.from(streetSet).sort();
});

const buildings = computed(() => {
  // 使用传递的真实数据，如果没有数据则返回空数组
  return props.buildingList || [];
});

// 筛选后的楼宇列表
const filteredBuildings = ref([]);

// 筛选楼宇
const filterBuildings = () => {
  let result = buildings.value;

  if (selectedStreet.value) {
    result = result.filter(building => building.streetName === selectedStreet.value);
  }

  if (selectedStar.value) {
    // 从星级名称中提取数字进行比较
    result = result.filter(building => {
      const starMatch = building.starLevel.match(/(\d+)/);
      const starNumber = starMatch ? starMatch[1] : '';
      return starNumber === selectedStar.value;
    });
  }

  // 保留所有筛选结果，通过表格高度控制显示6条，超出部分可滚动查看
  filteredBuildings.value = result;
};

// 选择楼宇
const selectBuilding = (building) => {
  selectedBuilding.value = building;
  // 可以触发事件给父组件
};

// 获取空置率样式类
const getVacancyClass = (rate) => {
  if (rate < 10) return 'low';
  if (rate < 15) return 'medium';
  return 'high';
};

// 获取星级样式类
const getStarClass = (starLevel) => {
  // 从星级名称中提取数字
  const starMatch = starLevel.match(/(\d+)/);
  const starNumber = starMatch ? starMatch[1] : '4';
  return `star-${starNumber}`;
};

// 格式化面积显示
const formatArea = (area) => {
  if (!area || area === 0) return '0';
  return Math.round(area).toLocaleString();
};

// 格式化百分比显示
const formatPercentage = (value) => {
  if (!value || value === 0) return '0';
  return Math.round(value);
};

// 自动滚动
let scrollInterval = null;
let isScrollPaused = false; // 滚动暂停状态
let scrollPauseCounter = 0; // 滚动暂停计数器

const startAutoScroll = () => {
  if (!tableBodyRef.value) return;
  
  scrollInterval = setInterval(() => {
    // 如果滚动被暂停，则不执行滚动
    if (isScrollPaused) return;

    const container = tableBodyRef.value;

    // 添加空值检查
    if (!container || !container.scrollHeight || !container.clientHeight) {
      return;
    }

    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;

    if (scrollHeight > clientHeight) {
      // 检查是否到达底部
      if (container.scrollTop >= scrollHeight - clientHeight - 1) {
        // 到达底部时暂停一下，让用户看清最后的数据
        scrollPauseCounter++;
        if (scrollPauseCounter >= 30) { // 暂停约2.4秒 (30 * 80ms)
          container.scrollTop = 0; // 重新开始滚动
          scrollPauseCounter = 0;
        }
      } else {
        container.scrollTop += 0.5; // 减慢滚动速度，让用户能更好地阅读
        scrollPauseCounter = 0; // 重置暂停计数器
      }
    }
  }, 80); // 增加间隔时间，让滚动更平滑
};

const stopAutoScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval);
    scrollInterval = null;
  }
};

// 暂停自动滚动（鼠标悬停时）
const pauseAutoScroll = () => {
  isScrollPaused = true;
  scrollPauseCounter = 0; // 重置暂停计数器
};

// 恢复自动滚动（鼠标离开时）
const resumeAutoScroll = () => {
  isScrollPaused = false;
  scrollPauseCounter = 0; // 重置暂停计数器
};

// 跳转到楼宇详情页
const goToBuildingDetail = (building) => {
  // 选中当前楼宇
  selectedBuilding.value = building;

  // 获取buildingNo，从原始数据中提取
  const buildingNo = building.originalData?.activeVO?.buildingNo || building.originalData?.basicVO?.buildingNo || building.id;

  console.log('跳转到楼宇详情页，buildingNo:', buildingNo, 'building:', building);

  // 跳转到楼宇详情页，传递楼宇信息和区域信息
  router.push({
    path: `/building/${building.id}`,
    query: {
      buildingId: building.id,
      buildingNo: buildingNo,
      buildingName: building.buildingName,
      district: props.selectedRegion.regionName,
      street: building.streetName,
      star: building.starLevel
    }
  });
};

onMounted(() => {
  filterBuildings();
  setTimeout(() => {
    startAutoScroll();
  }, 2000);
});

onUnmounted(() => {
  stopAutoScroll();
});

// 监听筛选条件变化
watch([selectedStreet, selectedStar], () => {
  filterBuildings();
});

// 监听buildingList数据变化
watch(() => props.buildingList, () => {
  console.log('buildingList数据变化，重新筛选:', props.buildingList);
  filterBuildings();
}, { deep: true });

// 监听区域变化
watch(() => props.selectedRegion, () => {
  selectedStreet.value = '';
  selectedStar.value = '';
  selectedBuilding.value = null;
  filterBuildings();
}, { deep: true });
</script>

<style scoped lang="less">
.district-building-list {
  height: 100%;
}

.border-box {
  height: 100%;
  padding: 10px 20px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-bg {
  position: absolute;
  z-index: -1;
  height: 40px;
  display: none;
}
.title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  height: 40px;
  width: 100%;
}

.section-title {
  color: #4fc3f7;
  font-size: 14px;
  font-weight: bold;
  margin-left: 40px;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

.filter-controls {
  display: flex;
  gap: 10px;
  margin-right: 20px;
}

.filter-select {
  background: rgba(79, 195, 247, 0.1);
  border: 1px solid rgba(79, 195, 247, 0.3);
  color: #999999;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  outline: none;
}

.filter-select:focus {
  border-color: #4fc3f7;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2.5fr 1.5fr 1fr 1.5fr 1fr;
  gap: 10px;
  padding: 10px;
  background: rgba(79, 195, 247, 0.1);
  border-radius: 6px 6px 0 0;
}

.header-cell {
  color: #4fc3f7;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
}

.table-body {
  flex: 1;
  overflow-y: auto;
  max-height: 360px; /* 显示6行数据的高度，超出部分可滚动 */
  min-height: 210px; /* 确保始终保持6行的高度 */
}

.table-body::-webkit-scrollbar {
  width: 4px;
}

.table-body::-webkit-scrollbar-track {
  background: rgba(79, 195, 247, 0.1);
}

.table-body::-webkit-scrollbar-thumb {
  background: rgba(79, 195, 247, 0.3);
  border-radius: 2px;
}

.table-row {
  display: grid;
  grid-template-columns: 2.5fr 1.5fr 1fr 1.5fr 1fr;
  gap: 10px;
  padding: 8px 10px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.table-row:hover {
  background: rgba(79, 195, 247, 0.05);
}

.table-row.selected {
  background: rgba(79, 195, 247, 0.1);
  border-color: rgba(79, 195, 247, 0.3);
}

.table-cell {
  color: #e3f2fd;
  font-size: 12px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.star-badge {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 9px;
  font-weight: bold;
}

.star-4 { background: #ff7043; color: white; }
.star-5 { background: #ffa726; color: white; }
.star-6 { background: #66bb6a; color: white; }
.star-7 { background: #4fc3f7; color: white; }

.vacancy-rate {
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 9px;
  font-weight: bold;
}

.vacancy-rate.low { background: #66bb6a; color: white; }
.vacancy-rate.medium { background: #ffa726; color: white; }
.vacancy-rate.high { background: #ff7043; color: white; }

/* 状态样式 */
.loading-container,
.error-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: rgba(79, 195, 247, 0.8);
  font-size: 14px;
}

.error-text {
  color: #ff7043;
}

.empty-text {
  color: rgba(79, 195, 247, 0.6);
}
</style>
