import { createApp } from 'vue'
import './styles/style.css'
import './styles/layout.css'
import App from './App.vue'
import router from './router'
import {createPinia} from 'pinia'

// 引入 Vue 3 兼容的可视化组件库
import DataVVue3 from '@kjgl77/datav-vue3'
import CountTo from 'vue3-count-to'

const app = createApp(App)
app.use(router)
app.use(createPinia())
app.use(DataVVue3)
app.component('CountTo', CountTo)
app.mount('#app')
