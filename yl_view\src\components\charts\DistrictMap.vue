<template>
  <div class="district-map">
    <div class="border-box">
      <div class="chart-container">
        <div class="map-container">
          <!-- ECharts地图容器 -->
          <div ref="mapRef" class="echarts-map"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import * as echarts from 'echarts';
import { getMapData } from '@/net/api.js';
import addressHooksData from '@/net/addressHooks.js';

// 路由实例
const router = useRouter();

// 接收父组件传递的选中区域信息和楼宇列表数据
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: '芙蓉区',
      level: 'district',
      areaData: null
    })
  },
  buildingList: {
    type: Array,
    default: () => []
  }
});

const mapRef = ref(null);
let chartInstance = null;

// 移除缓存，使用直接坐标获取

// DataV地图API地址（使用代理）
const publicUrl = "/api/maps/";

// 区域代码映射
const districtAdcodes = {
  '芙蓉区': 430102,
  '天心区': 430103,
  '岳麓区': 430104,
  '开福区': 430105,
  '雨花区': 430111,
  '望城区': 430112,
  '长沙县': 430121
};

// 各区域的默认坐标范围（用于生成随机坐标）
const districtCoordinateRanges = {
  '芙蓉区': {
    lng: [112.98, 113.05],
    lat: [28.17, 28.22],
    center: [113.010, 28.195]
  },
  '天心区': {
    lng: [112.95, 113.00],
    lat: [28.14, 28.20],
    center: [112.975, 28.170]
  },
  '岳麓区': {
    lng: [112.82, 112.95],
    lat: [28.14, 28.25],
    center: [112.885, 28.195]
  },
  '开福区': {
    lng: [112.99, 113.06],
    lat: [28.20, 28.27],
    center: [113.025, 28.235]
  },
  '雨花区': {
    lng: [113.04, 113.12],
    lat: [28.11, 28.18],
    center: [113.080, 28.145]
  },
  '望城区': {
    lng: [112.81, 112.88],
    lat: [28.34, 28.41],
    center: [112.845, 28.375]
  },
  '长沙县': {
    lng: [113.11, 113.23],
    lat: [28.18, 28.25],
    center: [113.170, 28.215]
  }
};

// 楼宇涟漪点数据 - 使用真实的长沙坐标
const buildingRippleData = {
  '芙蓉区': [
    { name: '芙蓉中央广场', coord: [113.025918, 28.194831], value: 234, star: 7 },
    { name: '华远国际中心', coord: [113.015918, 28.184695], value: 189, star: 6 },
    { name: '芙蓉广场', coord: [112.999634, 28.194831], value: 156, star: 5 },
    { name: '定王台商务中心', coord: [113.007826, 28.187354], value: 198, star: 6 },
    { name: '芙蓉国际大厦', coord: [113.010349, 28.187739], value: 167, star: 5 },
    { name: '火星商务大厦', coord: [113.039844, 28.207334], value: 203, star: 6 },
    { name: '马王堆国际中心', coord: [113.010653, 28.203472], value: 245, star: 7 }
  ],
  '天心区': [
    { name: '天心阁商务中心', coord: [112.969975, 28.189106], value: 187, star: 6 },
    { name: '城南国际大厦', coord: [112.969326, 28.174152], value: 145, star: 5 },
    { name: '裕南商务楼', coord: [112.979877, 28.156168], value: 123, star: 4 },
    { name: '金盆岭中心', coord: [112.969719, 28.165343], value: 167, star: 5 },
    { name: '新开铺科技园', coord: [112.959975, 28.149106], value: 198, star: 6 },
    { name: '青园商务广场', coord: [112.979975, 28.139106], value: 234, star: 7 }
  ],
  '岳麓区': [
    { name: '麓谷企业广场', coord: [112.869975, 28.189106], value: 267, star: 7 },
    { name: '银盆岭商务中心', coord: [112.939975, 28.199106], value: 198, star: 6 },
    { name: '观沙岭科技园', coord: [112.919975, 28.179106], value: 156, star: 5 },
    { name: '岳麓山大厦', coord: [112.939975, 28.189106], value: 189, star: 6 },
    { name: '梅溪湖国际中心', coord: [112.829975, 28.169106], value: 298, star: 7 },
    { name: '洋湖总部基地', coord: [112.849975, 28.149106], value: 234, star: 6 }
  ],
  '开福区': [
    { name: '开福万达广场', coord: [113.019975, 28.229106], value: 212, star: 6 },
    { name: '东风路商务中心', coord: [113.009975, 28.219106], value: 167, star: 5 },
    { name: '清水塘科技园', coord: [112.999975, 28.209106], value: 134, star: 4 },
    { name: '新河国际大厦', coord: [113.029975, 28.239106], value: 256, star: 7 },
    { name: '湘雅医疗产业园', coord: [113.039975, 28.249106], value: 189, star: 6 },
    { name: '伍家岭商务楼', coord: [113.049975, 28.259106], value: 145, star: 5 }
  ],
  '雨花区': [
    { name: '侯家塘商务楼', coord: [113.049975, 28.169106], value: 156, star: 5 },
    { name: '左家塘中心', coord: [113.059975, 28.159106], value: 189, star: 6 },
    { name: '圭塘企业广场', coord: [113.069975, 28.149106], value: 267, star: 7 },
    { name: '洞井科技园', coord: [113.079975, 28.139106], value: 123, star: 4 },
    { name: '高桥商务中心', coord: [113.089975, 28.129106], value: 198, star: 6 },
    { name: '井湾子总部大厦', coord: [113.099975, 28.119106], value: 234, star: 7 }
  ],
  '望城区': [
    { name: '高塘岭商务中心', coord: [112.819975, 28.349106], value: 134, star: 5 },
    { name: '金山桥科技园', coord: [112.829975, 28.359106], value: 112, star: 4 },
    { name: '大泽湖产业园', coord: [112.839975, 28.369106], value: 167, star: 6 },
    { name: '月亮岛大厦', coord: [112.849975, 28.379106], value: 189, star: 7 },
    { name: '铜官窑文创园', coord: [112.859975, 28.389106], value: 145, star: 5 },
    { name: '雷锋新城商务楼', coord: [112.869975, 28.399106], value: 156, star: 6 }
  ],
  '长沙县': [
    { name: '星沙商务广场', coord: [113.119975, 28.239106], value: 189, star: 6 },
    { name: '湘龙科技城', coord: [113.129975, 28.229106], value: 267, star: 7 },
    { name: '黄兴产业园', coord: [113.139975, 28.219106], value: 145, star: 5 },
    { name: '江背企业中心', coord: [113.149975, 28.209106], value: 123, star: 4 },
    { name: '黄花机场商务区', coord: [113.219975, 28.199106], value: 198, star: 6 },
    { name: '泉塘总部基地', coord: [113.169975, 28.189106], value: 234, star: 7 }
  ]
};

// 处理楼宇列表数据，获取涟漪点数据
const processRippleData = () => {
  if (!props.buildingList || props.buildingList.length === 0) {
    const regionName = props.selectedRegion.regionName;
    return buildingRippleData[regionName] || [];
  }
  const rippleData = [];
  const regionName = props.selectedRegion.regionName;

  // 直接处理所有楼宇，不需要异步等待
  props.buildingList.forEach((building) => {
    try {
      const address = building.address || '';
      const buildingName = building.buildingName || building.name || '';

      if (!buildingName) {
        return;
      }

      // 使用addressHooks.js匹配楼宇坐标
      const coordinates = getCoordinatesForBuilding(buildingName);

      // 只有找到坐标的楼宇才添加到地图上显示
      if (coordinates) {
        rippleData.push({
          name: buildingName,
          coord: coordinates,
          value: building.companyCount || building.companies || 100, // 企业数量
          star: building.starLevel || building.star || 5,
          buildingId: building.id,
          address: address,
          street: building.streetName || building.street || '',
          originalData: building // 保存原始数据
        });
      } else {
        console.log(`楼宇 "${buildingName}" 未找到坐标，不在地图上显示`);
      }
    } catch (error) {
    }
  });

  // 统计匹配结果
  const exactMatches = rippleData.filter(item =>
    addressHooksData.some(hook => hook.name === item.name)
  ).length;

  const fuzzyMatches = rippleData.length - exactMatches;
  const skippedBuildings = props.buildingList.length - rippleData.length;
  // 如果没有成功处理任何楼宇数据，使用静态数据作为后备
  // if (rippleData.length === 0) {
  //   console.warn('所有楼宇处理都失败，使用静态数据');
  //   return buildingRippleData[regionName] || [];
  // }

  return rippleData;
};

// 获取星级对应的颜色
const getStarColor = (star) => {
  const colors = {
    4: '#ff7043',
    5: '#ffa726',
    6: '#66bb6a',
    7: '#4fc3f7'
  };
  return colors[star] || '#4fc3f7';
};

// 移除高德API检查，直接使用addressHooks.js中的坐标数据

// 智能获取楼宇坐标 - 使用addressHooks.js中的经纬度数据
const getCoordinatesForBuilding = (buildingName) => {
  if (!buildingName) {
    return null;
  }

  // 1. 精确匹配addressHooks.js中的楼宇名称
  const exactMatch = addressHooksData.find(item => item.name === buildingName);
  if (exactMatch) {
    const coordinates = [exactMatch.longitude, exactMatch.latitude];
    return coordinates;
  }

  // 2. 模糊匹配addressHooks.js中的楼宇名称
  const fuzzyMatch = addressHooksData.find(item =>
    item.name.includes(buildingName) || buildingName.includes(item.name)
  );

  if (fuzzyMatch) {
    const coordinates = [fuzzyMatch.longitude, fuzzyMatch.latitude];
    return coordinates;
  }

  // 3. 如果没有匹配到，返回null，不在地图上显示
  return null;
};

// 获取各区域的地图配置 - 使用区域中心点而不是楼宇位置
const getMapConfig = (regionName) => {
  const configs = {
    // 各区域的地理中心点坐标，确保地图居中显示
    '芙蓉区': { zoom: 1.5, center: null }, // 使用默认中心
    '天心区': { zoom: 1.5, center: null },
    '岳麓区': { zoom: 1.3, center: null },
    '开福区': { zoom: 1.4, center: null },
    '雨花区': { zoom: 1.5, center: null },
    '望城区': { zoom: 1.2, center: null },
    '长沙县': { zoom: 1.3, center: null }
  };
  return configs[regionName] || { zoom: 1.5, center: null };
};

// 加载区域地图
const loadDistrictMap = async () => {
  if (!mapRef.value) return;
  
  const regionName = props.selectedRegion.regionName;
  const adcode = districtAdcodes[regionName];
  
  if (!adcode) {
    return;
  }

  try {
    // 获取地图数据
    const apiUrl = `${publicUrl}${adcode}.json`;
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`HTTP错误! 状态码: ${response.status}`);
    }
    
    const mapData = await response.json();
    
    // 注册地图
    echarts.registerMap(regionName, mapData);

    // 获取涟漪点数据
    const rippleData = processRippleData();

    // 获取地图配置
    const mapConfig = getMapConfig(regionName);

    // 构建geo配置对象
    const geoConfig = {
      map: regionName,
      roam: true,
      zoom: mapConfig.zoom,
      layoutCenter: ['50%', '50%'],
      layoutSize: '95%',
      itemStyle: {
        areaColor: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(3,27,78,0.8)' },
            { offset: 1, color: 'rgba(58,149,253,0.3)' }
          ]
        },
        borderColor: '#4fc3f7',
        borderWidth: 1
      },
      emphasis: {
        itemStyle: {
          areaColor: 'rgba(79, 195, 247, 0.3)'
        }
      }
    };

    // 只有当center不为null时才设置center属性
    if (mapConfig.center) {
      geoConfig.center = mapConfig.center;
    }

    // 配置地图选项
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          if (params.seriesType === 'effectScatter') {
            const data = params.data;
            return `
              <div style="color: #4fc3f7; font-weight: bold; margin-bottom: 5px;">${data.name}</div>
              <div style="color: #e3f2fd;">星级: ${data.star}星</div>
              <div style="color: #e3f2fd;">企业数量: ${data.value[2]}家</div>
              <div style="color: #e3f2fd;">坐标: [${data.coord[0].toFixed(4)}, ${data.coord[1].toFixed(4)}]</div>
            `;
          }
          return params.name;
        },
        backgroundColor: 'rgba(0, 20, 50, 0.9)',
        borderColor: '#4fc3f7',
        borderWidth: 1,
        textStyle: { color: '#fff' },
        padding: 10,
        extraCssText: 'box-shadow: 0 0 15px rgba(79, 195, 247, 0.5);'
      },
      geo: geoConfig,
      series: [
        {
          name: '楼宇分布',
          type: 'effectScatter',
          coordinateSystem: 'geo',
          data: rippleData.map(item => ({
            name: item.name,
            value: item.coord.concat(item.value),
            coord: item.coord,
            star: item.star,
            itemStyle: {
              color: getStarColor(item.star)
            }
          })),
          symbolSize: (val) => Math.max(8, Math.min(16, val[2] / 12)), /* 减小涟漪点尺寸 */
          showEffectOn: 'render',
          rippleEffect: {
            brushType: 'stroke',
            scale: 4, /* 减小涟漪效果 */
            period: 4
          },
          label: {
            show: false, /* 默认隐藏标签 */
            position: 'right',
            color: '#fff',
            fontSize: 12,
            formatter: '{b}',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            padding: [4, 6],
            borderRadius: 4,
            borderColor: '#4fc3f7',
            borderWidth: 1
          },
          emphasis: {
            scale: true,
            label: {
              show: true, /* 鼠标悬浮时显示标签 */
              fontSize: 14,
              color: '#4fc3f7',
              backgroundColor: 'rgba(0, 20, 50, 0.9)',
              padding: [6, 8],
              borderRadius: 6,
              borderColor: '#4fc3f7',
              borderWidth: 1,
              shadowBlur: 10,
              shadowColor: 'rgba(79, 195, 247, 0.5)'
            }
          }
        }
      ]
    };
    
    // 设置图表选项
    chartInstance.setOption(option);
    
  } catch (error) {
  }
};

// 初始化图表
const initChart = () => {
  if (mapRef.value && !chartInstance) {
    chartInstance = echarts.init(mapRef.value);

    // 添加点击事件监听
    chartInstance.on('click', (params) => {
      try {

        // 如果点击的是楼宇涟漪点
        if (params.seriesType === 'effectScatter' && params.data) {
          const buildingData = params.data;
          console.log('点击了楼宇涟漪点:', buildingData);

          // 跳转到楼宇详情页
          goToBuildingDetail(buildingData);
        }
      } catch (error) {
      }
    });

    loadDistrictMap();
  }
};

// 跳转到楼宇详情页
const goToBuildingDetail = (buildingData) => {
  try {

    // 从楼宇数据中提取信息
    const originalData = buildingData.originalData || {};
    const buildingNo = originalData.activeVO?.buildingNo || originalData.basicVO?.buildingNo || buildingData.buildingId;

    const buildingInfo = {
      id: buildingData.buildingId || Date.now(),
      name: buildingData.name || '未知楼宇',
      district: props.selectedRegion.regionName,
      street: buildingData.street || '未知街道',
      star: buildingData.star || 5,
      buildingNo: buildingNo
    };

    // 跳转到楼宇详情页
    router.push({
      path: `/building/${buildingInfo.id}`,
      query: {
        buildingId: buildingInfo.id,
        buildingNo: buildingInfo.buildingNo,
        buildingName: buildingInfo.name,
        district: buildingInfo.district,
        street: buildingInfo.street,
        star: buildingInfo.star,
        address: buildingData.address || ''
      }
    });
  } catch (error) {
  }
};

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 监听区域变化
watch(() => props.selectedRegion, () => {
  if (chartInstance) {
    loadDistrictMap();
  }
}, { deep: true });

// 监听楼宇列表数据变化
watch(() => props.buildingList, () => {
  if (chartInstance) {
    loadDistrictMap();
  }
}, { deep: true });

onMounted(() => {
  nextTick(() => {
    initChart();
    window.addEventListener('resize', handleResize);
  });
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped lang="less">
.district-map {
  height: 100%;
}

.border-box {
  height: 100%;
  padding: 10px; /* 添加较小的内边距 */
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px; /* 减小标题下边距 */
  height: 40px;
  width: 100%;
  background-image: url('@/assets/icon/smalltitle.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 60% 100%;
}

.section-title {
  color: #4fc3f7;
  font-size: 14px;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
  margin: 0;
}

.map-container {
  flex: 1;
  min-height: 0;
  border-radius: 8px;
  padding: 5px; /* 减小地图容器内边距 */
}

.echarts-map {
  height: 100%;
  width: 100%;
}
</style>
