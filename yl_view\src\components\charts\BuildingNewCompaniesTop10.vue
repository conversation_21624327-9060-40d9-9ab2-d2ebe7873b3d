<template>
  <div class="building-new-companies-top10">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="楼宇新增企业TOP10" />
        
        <div class="chart-content">
          <div
            class="companies-list"
            ref="companiesListRef"
            @mouseenter="pauseScroll"
            @mouseleave="resumeScroll"
          >
            <div
              v-for="(item, index) in companiesData"
              :key="index"
              class="companies-item"
            >
                <div
                class="ranking-number"
                :class="'rank-' + (index + 1)"
              >{{ String(index + 1).padStart(2, '0') }}</div>
              <div class="building-info">
                <div class="building-name">{{ item.name }}</div>
                <div class="building-location">{{ item.location }}</div>
              </div>
              <div class="count-section">
                <div class="count-display">
                  <div class="count-value">{{ item.count }}</div>
                  <div class="count-unit">家</div>
                </div>
                <div class="trend-indicator" :class="item.trend">
                  <span class="trend-icon">{{ item.trend === 'up' ? '↗' : '↘' }}</span>
                  <span class="trend-text">{{ item.trendValue }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import ChartTitle from '@/components/common/ChartTitle.vue';

// 模拟新增企业数据
const companiesData = ref([
  { name: '佳兆业广场', location: '芙蓉区', count: 28, trend: 'up', trendValue: '+5' },
  { name: '万达广场', location: '岳麓区', count: 25, trend: 'up', trendValue: '+3' },
  { name: '海信广场', location: '开福区', count: 23, trend: 'up', trendValue: '+7' },
  { name: '步步高梅溪新天地', location: '岳麓区', count: 21, trend: 'up', trendValue: '+2' },
  { name: '友谊商城', location: '芙蓉区', count: 19, trend: 'down', trendValue: '-1' },
  { name: '华润万象城', location: '开福区', count: 18, trend: 'up', trendValue: '+4' },
  { name: '悦方ID MALL', location: '岳麓区', count: 16, trend: 'up', trendValue: '+6' },
  { name: '德思勤城市广场', location: '雨花区', count: 15, trend: 'up', trendValue: '+1' },
  { name: '砂之船奥莱', location: '望城区', count: 13, trend: 'down', trendValue: '-2' },
  { name: '长沙IFS国金中心', location: '芙蓉区', count: 12, trend: 'up', trendValue: '+3' },
  { name: '步步高广场', location: '天心区', count: 11, trend: 'up', trendValue: '+2' },
  { name: '平和堂', location: '芙蓉区', count: 10, trend: 'down', trendValue: '-1' },
  { name: '王府井百货', location: '天心区', count: 9, trend: 'up', trendValue: '+1' },
  { name: '奥克斯广场', location: '雨花区', count: 8, trend: 'up', trendValue: '+3' },
  { name: '红星美凯龙', location: '岳麓区', count: 7, trend: 'down', trendValue: '-2' }
]);

// 自动滚动相关
const companiesListRef = ref(null);
let scrollInterval = null;
let isScrollPaused = false;

// 开始自动滚动
const startAutoScroll = () => {
  if (scrollInterval) return;

  scrollInterval = setInterval(() => {
    if (!isScrollPaused && companiesListRef.value) {
      const container = companiesListRef.value;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;
      const currentScrollTop = container.scrollTop;

      // 如果滚动到底部，回到顶部
      if (currentScrollTop + clientHeight >= scrollHeight - 20) {
        container.scrollTop = 0;
      } else {
        // 平滑滚动
        container.scrollTop += 1;
      }
    }
  }, 50); // 每50ms滚动1px
};

// 停止自动滚动
const stopAutoScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval);
    scrollInterval = null;
  }
};

// 暂停滚动
const pauseScroll = () => {
  isScrollPaused = true;
};

// 恢复滚动
const resumeScroll = () => {
  isScrollPaused = false;
};

onMounted(() => {
  // 延迟启动自动滚动，确保DOM已渲染
  setTimeout(() => {
    startAutoScroll();
  }, 1000);
});

onUnmounted(() => {
  stopAutoScroll();
});
</script>

<style scoped lang="less">
.building-new-companies-top10 {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.border-box {
  height: 100%;
  border-radius: 12px;
  padding: 15px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 15px;
  height: 40px;
  width: 100%;
}

/* 原有的标题样式已移至ChartTitle组件 */

.chart-content {
  flex: 1;
  overflow: hidden;
}

.companies-list {
  height: 100%;
  overflow-y: auto;
  padding-right: 5px;
}

.companies-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(79, 195, 247, 0.1);
  gap: 12px;
}

.companies-item:last-child {
  border-bottom: none;
}

.rank-number {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #66bb6a, #81c784);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.building-info {
  flex: 1;
  min-width: 0;
}

.building-name {
  color: #e3f2fd;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.building-location {
  color: #81d4fa;
  font-size: 11px;
  opacity: 0.8;
  margin-top: 2px;
}

.count-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  min-width: 80px;
}

.count-display {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.count-value {
  color: #66bb6a;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(102, 187, 106, 0.5);
}

.count-unit {
  color: #81c784;
  font-size: 12px;
  opacity: 0.8;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}

.trend-indicator.up {
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.trend-indicator.down {
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.trend-icon {
  font-size: 12px;
}

.trend-text {
  font-weight: 500;
}

/* 数字动画效果 */
.count-value {
  position: relative;
  overflow: hidden;
}

.count-value::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 187, 106, 0.3), transparent);
  animation: numberShimmer 3s infinite;
}

@keyframes numberShimmer {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

/* 滚动条样式 */
.companies-list::-webkit-scrollbar {
  width: 4px;
}

.companies-list::-webkit-scrollbar-track {
  background: rgba(79, 195, 247, 0.1);
  border-radius: 2px;
}

.companies-list::-webkit-scrollbar-thumb {
  background: rgba(79, 195, 247, 0.5);
  border-radius: 2px;
}

.companies-list::-webkit-scrollbar-thumb:hover {
  background: rgba(79, 195, 247, 0.7);
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .building-name {
    font-size: 12px;
  }
  
  .building-location {
    font-size: 10px;
  }
  
  .count-value {
    font-size: 16px;
  }
  
  .count-unit {
    font-size: 11px;
  }
}
</style>
