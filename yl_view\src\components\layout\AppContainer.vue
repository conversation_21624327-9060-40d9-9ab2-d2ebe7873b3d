<template>
  <div class="app-container">
    <!-- 固定的顶部标题区域 -->
    <div class="page-header">
      <!-- 左侧内容 -->
      <div class="header-left">
        <slot name="header-left">
          <!-- 默认内容 -->
        </slot>
      </div>

      <!-- 中央主标题 -->
      <div class="header-center">
        <div class="title-decoration">
          <div class="main-title-wrapper">
            <div class="main-title">楼宇经济AI产业互联服务平台</div>
          </div>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="header-right">
        <slot name="header-right">
          <!-- 默认内容 -->
        </slot>
      </div>
    </div>

    <!-- 页面内容区域 -->
    <div class="page-content">
      <slot name="content">
        <!-- 页面内容插槽 -->
      </slot>
    </div>
  </div>
</template>

<script setup>
// 这个组件只负责提供布局结构，不包含具体逻辑
</script>

<style scoped lang="less">
.app-container {
  width: 100vw;
  height: 100vh;
  background: url(../../assets/img/bg4.jpg);
  background-size: cover;
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 120px;
  padding: 0 40px;
  // backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

/* 头部装饰背景 */
.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url(../../assets/icon/top.svg) center/cover no-repeat;
  background-size: 100% 100%;
  opacity: 0.6;
  z-index: 1;
  mix-blend-mode: screen;
}

/* 头部额外装饰层 */
.page-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(79, 195, 247, 0.1) 25%,
    rgba(79, 195, 247, 0.15) 50%,
    rgba(79, 195, 247, 0.1) 75%,
    transparent 100%);
  z-index: 2;
  pointer-events: none;
}

.page-header > * {
  position: relative;
  z-index: 3;
}

/* 左侧区域 */
.header-left {
  flex: 1;
  display: flex;
  align-items: center;
}

/* 中央标题区域 */
.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.title-decoration {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 500px;
}

.main-title-wrapper {
  text-align: center;
  white-space: nowrap;
  z-index: 10;
  position: relative;
}

.main-title {
  color: #4fc3f7;
  font-size: 27px;
  font-weight: bold;
  text-shadow: 0 0 20px rgba(79, 195, 247, 0.8);
  letter-spacing: 4px;
  margin-bottom: 4px;
  background: linear-gradient(135deg, #4fc3f7, #81d4fa, #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% { text-shadow: 0 0 20px rgba(79, 195, 247, 0.8); }
  100% { text-shadow: 0 0 30px rgba(79, 195, 247, 1), 0 0 40px rgba(129, 212, 250, 0.6); }
}

/* 右侧区域 */
.header-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* 页面内容区域 */
.page-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}
</style>
