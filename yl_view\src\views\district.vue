<template>
  <div class="district-view district-layout">
    <!-- 主要内容区域 -->
    <div class="unified-main-content">
      <!-- 左侧面板 -->
      <div class="unified-panel unified-left-panel">
        <!-- 区内楼宇信息统计 -->
        <div class="unified-chart-section">
          <DistrictBuildingStats
            :selectedRegion="selectedRegion"
            :summaryData="buildingSummaryData"
          />
        </div>

        <!-- 区域租赁均价及空置率 -->
        <div class="unified-chart-section">
          <DistrictRentVacancyChart :selectedRegion="selectedRegion" />
        </div>
      </div>

      <!-- 中间面板 -->
      <div class="unified-panel unified-center-panel">
        <!-- 核心数据统计区域 -->
        <div class="data-stats-section">
          <div class="stats-container">
            <!-- 统计数据 -->
            <DistrictCoreDataCompact
              :selectedRegion="selectedRegion"
              :summaryData="buildingSummaryData"
            />
          </div>
        </div>

        <!-- 区级地图 -->
        <div class="unified-chart-section map-section">
          <DistrictMap
            :selectedRegion="selectedRegion"
            :buildingList="buildingListData"
          />
        </div>

        <!-- 区内楼宇列表（减少高度） -->
        <div class="unified-chart-section building-list-section-compact">
          <DistrictBuildingList
            :selectedRegion="selectedRegion"
            :buildingList="buildingListData"
            :loading="loading"
            :error="error"
          />
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="unified-panel unified-right-panel">
        <!-- 行业分布柱状图 -->
        <div class="unified-chart-section">
          <IndustryDistribution3DPie :selectedRegion="selectedRegion" />
        </div>

        <!-- 星级分布饼图 -->
        <div class="unified-chart-section">
          <StarDistribution3DPie :selectedRegion="selectedRegion" />
        </div>

        <!-- 绿色建筑认证 -->
        <div class="unified-chart-section">
          <GreenBuildingChart
            :selectedRegion="selectedRegion"
            :greenAuthList="buildingSummaryData.greenAuthList"
            :leedAuthList="buildingSummaryData.leedAuthList"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';

// 引入API接口
import { queryBuildingInfoByPage } from '@/net/districtApi.js';
import { getDistrictsInfo, getStreetsInfo, getBuildingLevels, queryBuildingSummary } from '@/net/api.js';

// 引入组件
import DistrictCoreDataCompact from '@/components/charts/DistrictCoreDataCompact.vue';
import DistrictBuildingStats from '@/components/charts/DistrictBuildingStats.vue';
import DistrictRentVacancyChart from '@/components/charts/DistrictRentVacancyChart.vue';
import DistrictBuildingList from '@/components/charts/DistrictBuildingList.vue';
import DistrictMap from '@/components/charts/DistrictMap.vue';
import StarDistribution3DPie from '@/components/charts/StarDistribution3DPie.vue';
import IndustryDistribution3DPie from '@/components/charts/IndustryDistribution3DPie.vue';
import GreenBuildingChart from '@/components/charts/GreenBuildingChart.vue';

const route = useRoute();

// 选中的区域信息
const selectedRegion = ref({
  regionName: '芙蓉区',
  level: 'district',
  areaData: null
});

// 楼宇列表数据状态
const buildingListData = ref(null);
const loading = ref(false);
const error = ref(null);

// 楼宇统计数据状态
const buildingSummaryData = ref({
  buildingNums: 0,
  investedArea: 0,
  vacantArea: 0,
  rentAmtAvg: 0,
  gnp: 0,
  gdp: 0,
  companyCounts: 0,
  greenAuthList: [],
  leedAuthList: []
});

// 字典数据
const districtsDict = ref([]);
const streetsDict = ref([]);
const buildingLevelsDict = ref([]);

// 获取所有字典数据
const fetchDictionaries = async () => {
  try {
    // 并行获取三个字典
    const [districtsResponse, streetsResponse, levelsResponse] = await Promise.all([
      getDistrictsInfo({}),
      getStreetsInfo({}),
      getBuildingLevels()
    ]);

    // 缓存字典数据
    districtsDict.value = districtsResponse.data || [];
    streetsDict.value = streetsResponse.data || [];
    buildingLevelsDict.value = levelsResponse.data || [];

  } catch (err) {
    console.error('获取字典数据失败:', err);
  }
};

// 根据区域名称获取区编码
const getDistrictCode = (districtName) => {
  const district = districtsDict.value.find(item =>
    item.districtName === districtName
  );
  return district ? district.districtNo : null;
};

// 处理楼宇列表数据
const processBuildingListData = (buildingList) => {
  if (!buildingList || !Array.isArray(buildingList)) {
    return [];
  }

  return buildingList.map((item, index) => {
    const basicVO = item.basicVO || {};
    const activeVO = item.activeVO || {};

    // 匹配街道字典获取街道名称
    const streetInfo = streetsDict.value.find(street =>
      street.streetNo === basicVO.streetNo
    );
    const streetName = streetInfo ? streetInfo.street : `未知街道(${basicVO.streetNo})`;

    // 匹配星级字典获取星级名称
    const levelInfo = buildingLevelsDict.value.find(level =>
      level.code === basicVO.buildingLevel
    );
    const starLevelName = levelInfo ? levelInfo.description : `${basicVO.buildingLevel}星级`;
    return {
      id: index + 1,
      buildingName: basicVO.buildingName || '未知楼宇',
      streetName: streetName,
      starLevel: starLevelName,
      buildingArea: basicVO.officeBuildingArea || 0,
      vacancyRate: activeVO.vacantRatio || 0,
      // 保留原始数据
      originalData: item
    };
  });
};

// 获取区内楼宇列表数据
const fetchBuildingListData = async () => {
  try {
    loading.value = true;
    error.value = null;
    // 获取区编码
    const districtCode = getDistrictCode(selectedRegion.value.regionName);
    if (!districtCode) {
      error.value = '未找到对应的区编码';
      return;
    }

    // 构建请求参数
    const params = {
      districtNo: districtCode,  // 使用区编码
      page: 1,                  // 页码
      rows: 20                  // 每页数量
    };
    const response = await queryBuildingInfoByPage(params);
    if (response.data && response.data.records) {

      // 处理楼宇列表数据
      const processedList = processBuildingListData(response.data.records);
      buildingListData.value = processedList;
    } else {
      console.warn('未获取到楼宇列表数据');
    }
  } catch (err) {
    console.error('获取区内楼宇列表数据失败:', err);
    error.value = '获取区内楼宇列表数据失败: ' + err.message;
  } finally {
    loading.value = false;
  }
};

// 获取楼宇统计数据
const fetchBuildingSummary = async () => {
  try {
    const districtCode = getDistrictCode(selectedRegion.value.regionName);
    if (!districtCode) {
      console.warn('未找到区域编码:', selectedRegion.value.regionName);
      return;
    }

    const response = await queryBuildingSummary({
      cityNo: '0731',
      districtNo: districtCode
    });

    if (response.code === 200 && response.data) {
      buildingSummaryData.value = {
        buildingNums: response.data.buildingNums || 0,
        investedArea: response.data.investedArea || 0,
        vacantArea: response.data.vacantArea || 0,
        rentAmtAvg: response.data.rentAmtAvg || 0,
        gnp: response.data.gnp || 0,
        gdp: response.data.gdp || 0,
        companyCounts: response.data.companyCounts || 0,
        greenAuthList: response.data.greenAuthList || [],
        leedAuthList: response.data.leedAuthList || []
      };
    } else {
      console.warn('获取楼宇统计数据失败:', response.message);
    }
  } catch (err) {
    console.error('获取楼宇统计数据失败:', err);
  }
};

onMounted(async () => {
  // 从路由参数或查询参数获取区域名称
  const regionName = route.params.region || route.query.regionName || '芙蓉区';
  selectedRegion.value.regionName = regionName;

  try {
    // 先获取字典数据
    await fetchDictionaries();

    // 再获取楼宇列表数据和统计数据
    await Promise.all([
      fetchBuildingListData(),
      fetchBuildingSummary()
    ]);
  } catch (err) {
    console.error('数据初始化失败:', err);
  }
});

onUnmounted(() => {
  // 页面卸载时的清理逻辑
});
</script>

<style scoped lang="less">
.district-view {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 移除所有头部相关样式，现在由AppContainer和头部组件处理 */

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 15px;
  gap: 15px;
  position: relative;
  z-index: 1;
  overflow: hidden;
  min-height: 0;
}

.top-section {
  height: 160px;
  border-radius: 12px;
  padding: 20px;
}

.middle-section {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1.3fr 1fr;
  gap: 15px;
  min-height: 0;
}

.left-panel,
.center-panel,
.right-panel {
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.center-panel {
  gap: 8px; /* 减少中间面板的间隙 */
}

.left-panel {
  gap: 10px; /* 减少左侧面板间隙，让三个组件更紧凑 */
}

.right-panel {
  gap: 10px; /* 减少右侧面板间隙，让三个组件更紧凑 */
}

.chart-section {
  flex: 1;
  min-height: 0;
  // background: rgba(0, 20, 50, 0.2);
  // backdrop-filter: blur(10px);
  // border: 1px solid rgba(79, 195, 247, 0.2);
  // border-radius: 12px;
  // overflow: hidden;
}

/* 数据统计区域样式（与首页保持一致） */
.data-stats-section {
  height: 160px;
  position: relative;
  overflow: hidden;
}

/* 科技感长方形边框 */
.data-stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 12px;
  z-index: 1;
}

.stats-container {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
  z-index: 2;
  padding: 0 30px; /* 长方形边框的标准内边距 */
}

/* 楼宇列表紧凑版样式 */
.building-list-section-compact {
  flex: 0.6; /* 减少高度占比 */
  min-height: 200px; /* 设置最小高度 */
  border-radius: 12px;
  overflow: hidden;
}

/* 左侧面板组件高度优化 */
.unified-left-panel .unified-chart-section:nth-child(1) {
  flex: 1; /* 进一步增加区内楼宇信息统计的比例以适应增加高度的8个指标 */
  min-height: 320px; /* 增加最小高度以适应增加了高度和间距的4行数据指标 */
}

.unified-left-panel .unified-chart-section:nth-child(2) {
  flex: 0.9; /* 相应减少区域租赁均价及空置率的比例 */
  min-height: 220px; /* 确保图表有足够空间 */
}

/* 右侧面板组件高度优化 */
.right-panel .chart-section {
  min-height: 180px; /* 确保组件有足够的显示空间 */
}

/* 核心数据区域 */
.right-panel .core-data-section {
  flex: 0.7; /* 核心数据占较小比例 */
  min-height: 160px;
}

/* 分布饼图左右布局 */
.right-panel .distribution-pies {
  flex: 0.8; /* 分布图占较大比例 */
  display: flex;
  gap: 8px;
  min-height: 200px;
}

/* 绿色建筑认证区域 */
.right-panel .chart-section:nth-child(3) {
  flex: 0.8; /* 绿色建筑认证占较小比例 */
  min-height: 180px;
}

.pie-half {
  flex: 1;
  min-height: 0;
}

.chart-section.building-list-section {
  height: 45%; /* 楼宇列表占45%高度 */
  min-height: 0;
}

.chart-section.map-section {
  height: 55%; /* 地图占55%高度 */
  min-height: 0;
}

/* 区级页面特殊样式 - 优化3D饼图显示 */
.right-panel .chart-section :deep(.border-box::before) {
  background-size: 100% !important; /* 稍微增大背景图 */
  opacity: 0.8 !important; /* 调整透明度 */
}

/* 优化3D饼图容器 */
.right-panel .chart-section :deep(.echarts-container) {
  height: calc(100% - 60px) !important; /* 为标题留出空间 */
  min-height: 180px !important;
}



/* 响应式设计 */
@media (max-width: 1920px) {
  .data-stats-section {
    height: 140px;
  }

  .building-list-section-compact {
    min-height: 180px;
  }
}

@media (max-width: 1600px) {
  .middle-section {
    grid-template-columns: 1fr 1.2fr 1fr;
  }

  /* 中等屏幕下调整左侧面板组件高度 */
  .left-panel .chart-section:first-child {
    min-height: 180px;
  }

  .left-panel .chart-section:last-child {
    min-height: 220px;
  }

  /* 中等屏幕下调整右侧面板组件高度 */
  .right-panel .chart-section {
    min-height: 200px;
    max-height: 320px;
  }

  .data-stats-section {
    height: 130px;
  }

  .building-list-section-compact {
    min-height: 160px;
  }
}

@media (max-width: 1400px) {
  .left-panel {
    gap: 12px; /* 进一步减少间隙 */
  }

  .right-panel {
    gap: 12px; /* 进一步减少右侧面板间隙 */
  }

  .right-panel .chart-section {
    min-height: 180px;
    max-height: 280px;
  }

  .data-stats-section {
    height: 120px;
  }

  .building-list-section-compact {
    min-height: 140px;
  }
}

@media (max-width: 1200px) {
  .middle-section {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }

  .top-section {
    height: auto;
    min-height: 100px;
  }

  /* 小屏幕下重置左侧面板布局 */
  .left-panel .chart-section:first-child,
  .left-panel .chart-section:last-child {
    flex: 1;
    min-height: 200px;
  }

  .left-panel {
    gap: 15px;
  }

  /* 小屏幕下重置右侧面板布局 */
  .right-panel {
    gap: 15px;
  }

  .right-panel .chart-section {
    min-height: 200px;
    max-height: none; /* 移除最大高度限制 */
  }

  .data-stats-section {
    height: 100px;
  }

  .building-list-section-compact {
    min-height: 120px;
  }
}
</style>
