<template>
  <div class="district-core-data-compact">
    <div class="stats-grid">
      <div class="stat-item" v-for="stat in statsData" :key="stat.label">
        <div class="stat-value">{{ stat.value }}</div>
        <div class="stat-label">{{ stat.label }}</div>
        <div class="stat-icon">
          <img src="@/assets/icon/dizuo.svg" class="icon-bg" alt="data icon">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// 接收父组件传递的选中区域信息和API数据
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: '芙蓉区',
      level: 'district',
      areaData: null
    })
  },
  summaryData: {
    type: Object,
    default: () => ({
      gnp: 0,
      gdp: 0,
      buildingNums: 0,
      companyCounts: 0
    })
  }
});

// 数据格式化函数
const formatNumber = (num) => {
  if (!num && num !== 0) return '0';

  // 如果是字符串且已经包含逗号，直接返回
  if (typeof num === 'string' && num.includes(',')) {
    return num;
  }

  // 转换为数字并格式化
  const number = parseFloat(num);
  if (isNaN(number)) return '0';

  // 对于大数字添加千分位分隔符
  if (number >= 1000) {
    return number.toLocaleString('zh-CN');
  }

  return number.toString();
};

// 统计数据（使用API数据）
const statsData = computed(() => [
  {
    label: '楼宇经济年度产值',
    value: formatNumber(props.summaryData.gnp) + '亿元'
  },
  {
    label: 'GDP总额',
    value: formatNumber(props.summaryData.gdp) + '亿元'
  },
  {
    label: '楼宇总数',
    value: formatNumber(props.summaryData.buildingNums) + '栋'
  },
  {
    label: '入驻企业总数',
    value: formatNumber(props.summaryData.companyCounts) + '家'
  }
]);
</script>

<style scoped lang="less">
.district-core-data-compact {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.stats-grid {
  display: flex;
  gap: 40px;
  align-items: center;
  justify-content: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-width: 120px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #4fc3f7;
  text-shadow: 0 0 15px rgba(79, 195, 247, 0.6);
  margin-bottom: 8px;
  z-index: 2;
  position: relative;
  animation: valueGlow 2s ease-in-out infinite alternate;
}

@keyframes valueGlow {
  0% {
    text-shadow: 0 0 15px rgba(79, 195, 247, 0.6);
  }
  100% {
    text-shadow: 0 0 25px rgba(79, 195, 247, 0.9);
  }
}

.stat-label {
  color: #e3f2fd;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
  z-index: 2;
  position: relative;
  text-align: center;
}

.stat-icon {
  position: absolute;
  bottom: -70px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.icon-bg {
  width: 120px;
  height: 120px;
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}


</style>
