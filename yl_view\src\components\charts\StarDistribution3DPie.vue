<template>
  <div class="star-vacancy-rate-chart">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="星级空置率" />

        <!-- 星级筛选器 -->
        <div class="star-filter">
          <div
            v-for="star in starLevels"
            :key="star.code"
            :class="['filter-item', { active: selectedStar === star.code }]"
            @click="selectStar(star.code)"
          >
            {{ star.description }}
          </div>
        </div>

        <div ref="vacancyChartRef" class="echarts-container"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue';
import * as echarts from 'echarts';
import ChartTitle from '@/components/common/ChartTitle.vue';

// 接收父组件传递的选中区域信息
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: '长沙市',
      level: 'city',
      areaData: null
    })
  }
});

// 图表相关变量
const vacancyChartRef = ref(null);
let vacancyChart = null;

// 星级筛选相关
const selectedStar = ref('all');
const starLevels = ref([
  { code: 'all', description: '综合' },
  { code: '4', description: '4星标准级' },
  { code: '5', description: '5星甲级' },
  { code: '6', description: '6星超甲级' },
  { code: '7', description: '7星顶级' }
]);

// 星级空置率数据（基于您提供的图表数据）
const starVacancyData = {
  '4': {
    '2023': {
      rentPrice: 4.9,
      avgRent: 92.7,
      vacancyRate: 33.9,
      totalArea: 127.7
    },
    '2024': {
      rentPrice: 5.4,
      avgRent: 82.3,
      vacancyRate: 39.0,
      totalArea: 127.7
    }
  },
  '5': {
    '2023': {
      rentPrice: 1.8,
      avgRent: 74.6,
      vacancyRate: 17.2,
      totalArea: 269.6
    },
    '2024': {
      rentPrice: 1.7,
      avgRent: 72.4,
      vacancyRate: 20.6,
      totalArea: 269.6
    }
  },
  '6': {
    '2023': {
      rentPrice: 4.4,
      avgRent: 61.6,
      vacancyRate: 15.5,
      totalArea: 168.7
    },
    '2024': {
      rentPrice: 5.1,
      avgRent: 57.6,
      vacancyRate: 17.3,
      totalArea: 168.7
    }
  },
  '7': {
    '2023': {
      rentPrice: 3.2,
      avgRent: 85.0,
      vacancyRate: 12.8,
      totalArea: 195.3
    },
    '2024': {
      rentPrice: 3.8,
      avgRent: 78.5,
      vacancyRate: 14.2,
      totalArea: 195.3
    }
  }
};

// 星级筛选函数
const selectStar = (starCode) => {
  selectedStar.value = starCode;
  updateChart();
};

// 获取当前显示的数据
const getCurrentData = () => {
  if (selectedStar.value === 'all') {
    // 显示所有星级的数据
    return Object.keys(starVacancyData).map(star => ({
      star: `${star}星级`,
      ...starVacancyData[star]
    }));
  } else {
    // 显示选中星级的数据
    return starVacancyData[selectedStar.value] ? [{
      star: `${selectedStar.value}星级`,
      ...starVacancyData[selectedStar.value]
    }] : [];
  }
};

// 初始化星级空置率图表
const initVacancyChart = () => {
  if (!vacancyChartRef.value) return;

  vacancyChart = echarts.init(vacancyChartRef.value);
  updateChart();
};

// 更新图表数据
const updateChart = () => {
  if (!vacancyChart) return;

  const currentData = getCurrentData();

  if (selectedStar.value === 'all') {
    // 显示所有星级的对比图表
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#4fc3f7',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        },
        formatter: function(params) {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach(param => {
            result += `${param.seriesName}: ${param.value}%<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: ['2023年空置率', '2024年空置率'],
        textStyle: {
          color: '#e3f2fd',
          fontSize: 14
        },
        bottom: '5%',
        left: 'center'
      },
      grid: {
        left: '15%',
        right: '10%',
        top: '15%',
        bottom: '25%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: currentData.map(item => item.star),
        axisLine: {
          lineStyle: {
            color: '#4fc3f7'
          }
        },
        axisLabel: {
          color: '#e3f2fd',
          fontSize: 13
        }
      },
      yAxis: {
        type: 'value',
        name: '空置率(%)',
        nameTextStyle: {
          color: '#e3f2fd',
          fontSize: 13
        },
        axisLine: {
          lineStyle: {
            color: '#4fc3f7'
          }
        },
        axisLabel: {
          color: '#e3f2fd',
          fontSize: 13,
          formatter: '{value}%'
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(79, 195, 247, 0.2)'
          }
        }
      },
      series: [
        {
          name: '2023年空置率',
          type: 'bar',
          data: currentData.map(item => item['2023'].vacancyRate),
          itemStyle: {
            color: '#ff7043',
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: '2024年空置率',
          type: 'bar',
          data: currentData.map(item => item['2024'].vacancyRate),
          itemStyle: {
            color: '#4fc3f7',
            borderRadius: [4, 4, 0, 0]
          }
        }
      ]
    };

    vacancyChart.setOption(option);
  } else {
    // 显示单个星级的详细数据
    const data = currentData[0];
    if (!data) return;

    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#4fc3f7',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        data: ['净吸纳量(万㎡)', '平均租金(元/㎡/月)', '空置率(%)'],
        textStyle: {
          color: '#e3f2fd',
          fontSize: 12
        },
        bottom: '2%',
        left: 'center'
      },
      grid: {
        left: '15%',
        right: '15%',
        top: '15%',
        bottom: '25%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['2023年', '2024年'],
        axisLine: {
          lineStyle: {
            color: '#4fc3f7'
          }
        },
        axisLabel: {
          color: '#e3f2fd',
          fontSize: 13
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '净吸纳量/租金',
          nameTextStyle: {
            color: '#e3f2fd',
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: '#4fc3f7'
            }
          },
          axisLabel: {
            color: '#e3f2fd',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(79, 195, 247, 0.2)'
            }
          }
        },
        {
          type: 'value',
          name: '空置率(%)',
          nameTextStyle: {
            color: '#e3f2fd',
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: '#ff7043'
            }
          },
          axisLabel: {
            color: '#e3f2fd',
            fontSize: 12,
            formatter: '{value}%'
          }
        }
      ],
      series: [
        {
          name: '净吸纳量(万㎡)',
          type: 'bar',
          data: [data['2023'].rentPrice, data['2024'].rentPrice],
          itemStyle: {
            color: '#66bb6a',
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: '平均租金(元/㎡/月)',
          type: 'line',
          data: [data['2023'].avgRent, data['2024'].avgRent],
          lineStyle: {
            color: '#ffa726',
            width: 3
          },
          itemStyle: {
            color: '#ffa726'
          },
          symbol: 'circle',
          symbolSize: 6
        },
        {
          name: '空置率(%)',
          type: 'line',
          yAxisIndex: 1,
          data: [data['2023'].vacancyRate, data['2024'].vacancyRate],
          lineStyle: {
            color: '#ff7043',
            width: 3
          },
          itemStyle: {
            color: '#ff7043'
          },
          symbol: 'circle',
          symbolSize: 6
        }
      ]
    };

    vacancyChart.setOption(option);
  }
};

// 处理窗口大小变化
const handleResize = () => {
  if (vacancyChart) vacancyChart.resize();
};

// 监听选中区域变化
watch(() => props.selectedRegion, () => {
  updateChart();
}, { deep: true });

onMounted(() => {
  // 初始化图表
  nextTick(() => {
    initVacancyChart();
  });

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (vacancyChart) {
    vacancyChart.dispose();
  }
});
</script>

<style scoped lang="less">
.star-vacancy-rate-chart {
  height: 100%;
}

.border-box {
  height: 100%;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.border-box::before {
  content: '';
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  pointer-events: none;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.star-filter {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin: 8px 0;
  z-index: 2;
  position: relative;
}

.filter-item {
  padding: 4px 12px;
  background: rgba(79, 195, 247, 0.1);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 15px;
  color: #e3f2fd;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.filter-item:hover {
  background: rgba(79, 195, 247, 0.2);
  border-color: rgba(79, 195, 247, 0.5);
  transform: translateY(-1px);
}

.filter-item.active {
  background: rgba(79, 195, 247, 0.3);
  border-color: #4fc3f7;
  color: #4fc3f7;
  text-shadow: 0 0 8px rgba(79, 195, 247, 0.6);
}

.echarts-container {
  flex: 1;
  width: 100%;
  min-height: 160px;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .filter-item {
    padding: 3px 10px;
    font-size: 10px;
  }

  .echarts-container {
    min-height: 140px;
  }
}

@media (max-width: 1200px) {
  .filter-item {
    padding: 2px 8px;
    font-size: 9px;
  }

  .echarts-container {
    min-height: 120px;
  }
}
</style>
