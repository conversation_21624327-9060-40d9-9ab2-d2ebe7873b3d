/**
 * 严肃声明：
 * 开源版本请务必保留此注释头信息，若删除我方将保留所有法律责任追究！
 * 本系统已申请软件著作权，受国家版权局知识产权以及国家计算机软件著作权保护！
 * 可正常分享和学习源码，不得用于违法犯罪活动，违者必究！
 * Copyright (c) 2020 陈尼克 all rights reserved.
 * 版权所有，侵权必究！
 */
import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '../router'

//  axios.defaults.baseURL = import.meta.env.NODE_ENV == 'development' ? import.meta.env.VITE_BASE_URL : (window.requestAPI || '/')
// axios.defaults.withCredentials = true
// axios.defaults.headers['X-Requested-With'] = 'XMLHttpRequest'
// axios.defaults.headers['token'] = localStorage.getItem('token') || ''
axios.defaults.baseURL = import.meta.env.VITE_BASE_URL
axios.defaults.headers['Content-Type'] = 'application/x-www-form-urlencoded' //'application/json'


//请求发送拦截，把数据发送给后台之前做些什么......
axios.interceptors.request.use((request) => {

  // //这个例子中data是loginName和password
  // let REQUEST_DATA = request.data
  // //统一进行qs模块转换
  // request.data = qs.stringify(REQUEST_DATA)
  // 暂时注释掉token传递
  // const token = localStorage.getItem('token')
  //  const uid = localStorage.getItem('uid')
  //  if(token){
  //    request.url += `?token=${token}&uid=${uid}`
  //  }
  // if (token) {
  //   if (request.url.indexOf('?') == -1)
  //     request.url += `?token=${token}`
  //   else request.url += `&token=${token}`
  // }

  //再发送给后台
  return request;

}, function (error) {
  // Do something with request error
  return Promise.reject(error);
});


axios.interceptors.response.use(res => {
  if (typeof res.data !== 'object') {
    ElMessage.error('服务器端异常！')
    return Promise.reject(res)
  }
  if (res.data.Code != 0) {
    if (res.data.message)
      ElMessage.error(res.data.message)
    if (res.data.resultCode == 416) {
      router.push({ path: '/' })
    }
    // 暂时注释掉token处理
    // if (res.data.data && window.location.hash == '#/') {
    //   localStorage.setItem('token', res.data.data)
    //   axios.defaults.headers['token'] = res.data.data
    // }
    return Promise.reject(res.data)
  }

  return res.data
}, err => {
  Promise.reject(err)
})

export default axios
