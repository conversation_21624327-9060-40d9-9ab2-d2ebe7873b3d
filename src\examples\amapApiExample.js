import { 
  getBuildingCoordinates, 
  getNearbyFacilities, 
  getSpecificFacilities, 
  getBuildingWithFacilities 
} from '../net/addressHooks.js';

/**
 * 高德地图API使用示例
 */

// 示例1：通过写字楼名称获取经纬度
async function example1() {
  console.log('=== 示例1：获取写字楼坐标 ===');
  
  const result = await getBuildingCoordinates('长沙国金中心');
  console.log('长沙国金中心坐标：', result);
  
  // 输出示例：
  // {
  //   success: true,
  //   longitude: 112.99371,
  //   latitude: 28.19533,
  //   formatted_address: "湖南省长沙市芙蓉区解放东路593号",
  //   level: "门牌号"
  // }
}

// 示例2：查找指定坐标周边500米内的所有配套设施
async function example2() {
  console.log('=== 示例2：查找周边配套设施 ===');
  
  const longitude = 112.99371;
  const latitude = 28.19533;
  
  const result = await getNearbyFacilities(longitude, latitude, 500);
  console.log('周边设施总数：', result.total);
  console.log('前5个设施：', result.facilities?.slice(0, 5));
  
  // 输出示例：
  // {
  //   success: true,
  //   total: 20,
  //   facilities: [
  //     {
  //       name: "星巴克咖啡",
  //       type: "餐饮服务;咖啡厅;咖啡厅",
  //       address: "解放东路593号国金中心1层",
  //       location: { longitude: 112.99380, latitude: 28.19540 },
  //       distance: "15",
  //       tel: "0731-********"
  //     }
  //   ]
  // }
}

// 示例3：查找特定类型的设施（如餐厅）
async function example3() {
  console.log('=== 示例3：查找特定类型设施 ===');
  
  const longitude = 112.99371;
  const latitude = 28.19533;
  
  // 查找餐厅
  const restaurants = await getSpecificFacilities(longitude, latitude, 'restaurant', 500);
  console.log('周边餐厅：', restaurants.facilities?.slice(0, 3));
  
  // 查找银行
  const banks = await getSpecificFacilities(longitude, latitude, 'bank', 500);
  console.log('周边银行：', banks.facilities?.slice(0, 3));
  
  // 查找地铁站
  const subways = await getSpecificFacilities(longitude, latitude, 'subway', 1000);
  console.log('周边地铁站：', subways.facilities);
}

// 示例4：综合查询 - 一次性获取建筑信息和周边设施
async function example4() {
  console.log('=== 示例4：综合查询 ===');
  
  const result = await getBuildingWithFacilities('华远国际中心', 500);
  console.log('建筑信息：', result.building);
  console.log('周边设施数量：', result.total_facilities);
  console.log('部分设施：', result.facilities?.slice(0, 5));
}

// 示例5：查询特定建筑周边的特定设施
async function example5() {
  console.log('=== 示例5：查询特定建筑的特定设施 ===');
  
  // 查询长沙绿地中心周边的餐厅
  const result = await getBuildingWithFacilities('长沙绿地中心T1栋', 500, 'restaurant');
  console.log('长沙绿地中心周边餐厅：', result.facilities);
}

// 示例6：批量查询多个建筑的周边设施
async function example6() {
  console.log('=== 示例6：批量查询 ===');
  
  const buildings = ['长沙国金中心', '华远国际中心', '长沙绿地中心T1栋'];
  
  for (const building of buildings) {
    const result = await getBuildingWithFacilities(building, 300, 'restaurant');
    console.log(`${building} 周边餐厅数量：`, result.total_facilities);
  }
}

// 示例7：实际应用场景 - 为写字楼评估配套设施完善度
async function example7() {
  console.log('=== 示例7：配套设施完善度评估 ===');
  
  const buildingName = '长沙国金中心';
  const radius = 500;
  
  // 获取建筑坐标
  const coordResult = await getBuildingCoordinates(buildingName);
  if (!coordResult.success) {
    console.log('获取坐标失败');
    return;
  }
  
  const { longitude, latitude } = coordResult;
  
  // 分类查询各种设施
  const facilityTypes = {
    '餐饮': 'restaurant',
    '购物': 'shopping', 
    '银行': 'bank',
    '医院': 'hospital',
    '地铁': 'subway',
    '停车': 'parking'
  };
  
  const facilityReport = {};
  
  for (const [typeName, typeCode] of Object.entries(facilityTypes)) {
    const result = await getSpecificFacilities(longitude, latitude, typeCode, radius);
    facilityReport[typeName] = {
      count: result.success ? result.total : 0,
      facilities: result.success ? result.facilities : []
    };
  }
  
  console.log(`${buildingName} 配套设施报告：`);
  for (const [type, data] of Object.entries(facilityReport)) {
    console.log(`${type}：${data.count}个`);
  }
  
  // 计算配套完善度评分（简单示例）
  const totalScore = Object.values(facilityReport).reduce((sum, data) => sum + Math.min(data.count, 5), 0);
  const maxScore = Object.keys(facilityReport).length * 5;
  const completenessScore = (totalScore / maxScore * 100).toFixed(1);
  
  console.log(`配套完善度评分：${completenessScore}%`);
}

// 运行所有示例
export async function runAllExamples() {
  await example1();
  await example2();
  await example3();
  await example4();
  await example5();
  await example6();
  await example7();
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples();
}
