<template>
  <!-- 楼宇页面左侧：返回按钮和楼宇信息 -->
  <div class="back-section">
    <div class="back-button" @click="goBack">
      <div class="back-icon-wrapper">
        <img src="@/assets/icon/goback.svg" class="back-icon" alt="返回" />
        <span class="back-text">返回</span>
      </div>
    </div>
    <!-- <div class="building-info">
      <div class="building-name">{{ buildingName }}</div>
      <div class="building-level">楼宇数据展示</div>
    </div> -->
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  route: Object,
  router: Object
});

// 获取楼宇名称
const buildingName = computed(() => {
  return props.route.query.name || '佳兆业广场';
});

// 返回区级页面
const goBack = () => {
  const district = props.route.query.district || '芙蓉区';
  props.router.push(`/district/${district}`);
};
</script>

<style scoped lang="less">
.back-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  transform: scale(1.05);
}

.back-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 100px;
  height: auto;
  border: none;
  outline: none;
  background: transparent;
}

.back-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 14px;
  font-weight: 600;
  text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
  pointer-events: none;
}

.building-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.building-name {
  color: #4fc3f7;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 1px;
}

.building-level {
  color: #81d4fa;
  font-size: 12px;
  opacity: 0.8;
}
</style>
