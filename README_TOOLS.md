# 批量查询工具使用指南

## 🎯 访问方式

### 方法1：直接URL访问
在浏览器地址栏输入以下URL：

```
# 工具中心页面（推荐）
http://localhost:3000/#/tools

# 批量查询坐标页面
http://localhost:3000/#/batch-query

# 配套设施查询页面
http://localhost:3000/#/facilities-search
```

### 方法2：通过浮动工具按钮
1. 在任何页面右上角都有一个**蓝色的"工具"按钮**
2. 点击按钮打开工具菜单
3. 选择需要的工具页面

### 方法3：在现有页面中集成
如果您想在现有页面中直接使用组件：

```vue
<template>
  <div>
    <!-- 批量查询组件 -->
    <BatchCoordinatesQuery />
    
    <!-- 配套设施查询组件 -->
    <BuildingFacilitiesSearch />
  </div>
</template>

<script>
import BatchCoordinatesQuery from '@/components/BatchCoordinatesQuery.vue';
import BuildingFacilitiesSearch from '@/components/BuildingFacilitiesSearch.vue';

export default {
  components: {
    BatchCoordinatesQuery,
    BuildingFacilitiesSearch
  }
};
</script>
```

## 🔧 功能说明

### 1. 批量查询坐标 (`/batch-query`)
- **功能**：批量获取写字楼的经纬度坐标
- **输入方式**：
  - 手动输入（每行一个建筑名称）
  - 文件上传（.txt或.csv文件）
  - 使用现有数据（项目中的写字楼数据）
- **设置选项**：
  - 城市选择（长沙、北京、上海等）
  - 批次大小（5、10、20个）
  - 延迟时间（避免API频率限制）
- **输出**：CSV格式的坐标数据

### 2. 配套设施查询 (`/facilities-search`)
- **功能**：查询写字楼周边的配套设施
- **查询范围**：300米、500米、800米、1000米
- **设施类型**：
  - 全部设施
  - 餐饮服务
  - 购物服务
  - 银行金融
  - 医疗服务
  - 地铁站
  - 停车场
- **输出**：设施列表和统计信息

### 3. 工具中心 (`/tools`)
- **功能**：集成所有工具的中心页面
- **包含**：
  - 批量查询坐标
  - 配套设施查询
  - 数据统计分析
  - 快速导航链接

## 📊 使用示例

### 批量查询示例
```
输入建筑名称：
长沙国金中心
华远国际中心
长沙绿地中心T1栋
润和金融中心
湖南商会大厦

输出结果：
Name,Status,Longitude,Latitude,Address
"长沙国金中心","Success","112.99371","28.19533","湖南省长沙市芙蓉区解放东路593号"
"华远国际中心","Success","112.969975","28.189106","湖南省长沙市天心区湘府中路..."
...
```

### API调用示例
```javascript
import { getBatchCoordinatesLarge } from '@/net/addressHooks.js';

// 批量查询
const result = await getBatchCoordinatesLarge([
  '长沙国金中心',
  '华远国际中心',
  '长沙绿地中心T1栋'
], '长沙', 10, 200);

console.log(`成功：${result.success_count}个`);
console.log(`失败：${result.failed_count}个`);
console.log(`成功率：${result.success_rate}`);
```

## 🚀 开发环境启动

如果项目还没有运行，请执行：

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

然后访问 `http://localhost:3000/#/tools`

## 🔑 API配置

工具使用高德地图API，API Key已配置：
```javascript
const AMAP_KEY = '81ea7b2440e2bddd9f6b540fa04c141d';
```

## 📝 注意事项

1. **API限制**：高德地图API有频率限制，建议设置适当的延迟时间
2. **批次大小**：单次最多查询10个地址，超过会自动分批
3. **数据格式**：确保输入的建筑名称准确，包含城市信息更佳
4. **网络环境**：需要能够访问高德地图API服务

## 🛠️ 故障排除

### 常见问题

1. **页面无法访问**
   - 检查路由配置是否正确
   - 确认开发服务器正在运行

2. **API查询失败**
   - 检查网络连接
   - 确认API Key有效
   - 检查API调用频率

3. **组件导入错误**
   - 检查文件路径是否正确
   - 确认组件已正确导出

### 调试方法

1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 检查Network面板的API请求状态
4. 使用Vue DevTools查看组件状态

## 📞 技术支持

如果遇到问题，请检查：
1. 浏览器控制台的错误信息
2. 网络请求是否成功
3. 组件是否正确加载

---

**快速开始**：直接访问 `http://localhost:3000/#/tools` 开始使用！
