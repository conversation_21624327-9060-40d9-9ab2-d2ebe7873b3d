<template>
  <div class="statistics-grid">
    <div class="stats-grid">
      <div class="stat-item" v-for="stat in statsData" :key="stat.label">
        <div class="stat-value">{{ stat.value }}</div>
        <div class="stat-label">{{ stat.label }}</div>
        <div class="stat-icon">
          <img src="@/assets/icon/dizuo.svg" class="icon-bg" alt="data icon">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// 接收父组件传递的核心数据
const props = defineProps({
  coreData: {
    type: Object,
    default: () => ({
      buildingNums: 0,
      buildingArea: 0,
      companyCounts: 0,
      investedArea: 0
    })
  }
});

// 格式化数字显示
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(2) + '万';
  }
  return num.toLocaleString();
};

// 统计数据（基于传入的核心数据）
const statsData = computed(() => [
  { label: '楼宇数量', value: formatNumber(props.coreData.buildingNums) },
  { label: '总建筑面积', value: formatNumber(props.coreData.buildingArea) },
  { label: '企业总数量', value: formatNumber(props.coreData.companyCounts) },
  { label: '待招商面积', value: formatNumber(props.coreData.investedArea) }
]);

// 移除模拟数据更新逻辑，现在使用真实API数据
</script>

<style scoped lang="less">
.statistics-grid {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.stats-grid {
  display: flex;
  gap: 40px;
  align-items: center;
  justify-content: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-width: 120px;
  // background-image: url("@/assets/icon/dataicon_2.svg");
  // background-size:cover;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #4fc3f7;
  text-shadow: 0 0 15px rgba(79, 195, 247, 0.6);
  margin-bottom: 8px;
  z-index: 2;
  position: relative;
  animation: valueGlow 2s ease-in-out infinite alternate;
}

@keyframes valueGlow {
  0% {
    text-shadow: 0 0 15px rgba(79, 195, 247, 0.6);
  }
  100% {
    text-shadow: 0 0 25px rgba(79, 195, 247, 0.9);
  }
}

.stat-label {
  color: #e3f2fd;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
  z-index: 2;
  position: relative;
  text-align: center;
}

.stat-icon {
  position: absolute;
  bottom: -70px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.icon-bg {
  width: 120px;
  height: 120px;
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}
</style>
