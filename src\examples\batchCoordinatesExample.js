import { 
  getBatchCoordinates, 
  getBatchCoordinatesLarge, 
  enrichBuildingDataWithCoordinates 
} from '../net/addressHooks.js';

/**
 * 批量查询写字楼经纬度示例
 */

// 示例1：小批量查询（10个以内）
async function example1_SmallBatch() {
  console.log('=== 示例1：小批量查询（10个以内）===');
  
  const buildingNames = [
    '长沙国金中心',
    '华远国际中心', 
    '长沙绿地中心T1栋',
    '润和金融中心',
    '湖南商会大厦',
    '万坤图财富广场',
    '新湖南大厦',
    '兴业IEC',
    '御邦国际广场',
    '运达中央广场A座'
  ];
  
  const result = await getBatchCoordinates(buildingNames);
  
  if (result.success) {
    console.log(`批量查询完成：总共${result.total}个，成功${result.success_count}个，失败${result.failed_count}个`);
    console.log('查询结果：');
    result.results.forEach(item => {
      if (item.success) {
        console.log(`✓ ${item.name}: (${item.longitude}, ${item.latitude}) - ${item.formatted_address}`);
      } else {
        console.log(`✗ ${item.name}: ${item.message}`);
      }
    });
  } else {
    console.log('批量查询失败：', result.message);
  }
  
  return result;
}

// 示例2：大批量查询（自动分批处理）
async function example2_LargeBatch() {
  console.log('\n=== 示例2：大批量查询（自动分批）===');
  
  // 模拟大量写字楼名称
  const buildingNames = [
    '长沙国金中心', '华远国际中心', '长沙绿地中心T1栋', '润和金融中心', '湖南商会大厦',
    '万坤图财富广场', '新湖南大厦', '兴业IEC', '御邦国际广场', '运达中央广场A座',
    '长房国际大厦', '中建广场', '中国石油长沙大厦', '香泽南湖大厦', '北辰A1写字楼',
    '骏达大厦', '河西王府井写字楼', '红橡国际', '金茂ICC', '长沙西中心',
    '嘉熙中心', '保利国际广场', '蓝湾国际', '富兴世界金融中心T2', '长沙通程国际大酒店',
    '天城国际广场', '友阿总部办公大楼', '长房时代国际', '华创国际广场', '长房时代天地'
  ];
  
  const result = await getBatchCoordinatesLarge(buildingNames, '长沙', 10, 200);
  
  if (result.success) {
    console.log(`\n大批量查询完成！`);
    console.log(`总计：${result.total}个建筑`);
    console.log(`成功：${result.success_count}个 (${result.success_rate})`);
    console.log(`失败：${result.failed_count}个`);
    
    // 显示成功的前5个结果
    const successResults = result.results.filter(r => r.success).slice(0, 5);
    console.log('\n成功查询的前5个结果：');
    successResults.forEach(item => {
      console.log(`${item.name}: (${item.longitude}, ${item.latitude})`);
    });
    
    // 显示失败的结果
    const failedResults = result.results.filter(r => !r.success);
    if (failedResults.length > 0) {
      console.log('\n查询失败的建筑：');
      failedResults.forEach(item => {
        console.log(`${item.name}: ${item.message}`);
      });
    }
  } else {
    console.log('大批量查询失败：', result.message);
  }
  
  return result;
}

// 示例3：从现有数据中enrichment坐标信息
async function example3_EnrichData() {
  console.log('\n=== 示例3：数据enrichment（为现有数据添加坐标）===');
  
  // 模拟现有的建筑数据（只有名称，没有坐标）
  const existingBuildingData = [
    { id: 1, name: '长沙国金中心', district: '芙蓉区', type: '甲级写字楼' },
    { id: 2, name: '华远国际中心', district: '天心区', type: '甲级写字楼' },
    { id: 3, name: '长沙绿地中心T1栋', district: '开福区', type: '超甲级写字楼' },
    { id: 4, name: '润和金融中心', district: '芙蓉区', type: '甲级写字楼' },
    { id: 5, name: '湖南商会大厦', district: '天心区', type: '乙级写字楼' },
    { id: 6, name: '万坤图财富广场', district: '雨花区', type: '甲级写字楼' },
    { id: 7, name: '新湖南大厦', district: '开福区', type: '甲级写字楼' },
    { id: 8, name: '兴业IEC', district: '天心区', type: '甲级写字楼' },
    { id: 9, name: '御邦国际广场', district: '天心区', type: '乙级写字楼' },
    { id: 10, name: '运达中央广场A座', district: '雨花区', type: '甲级写字楼' }
  ];
  
  console.log('原始数据（无坐标）：');
  console.log(existingBuildingData.slice(0, 3));
  
  const result = await enrichBuildingDataWithCoordinates(existingBuildingData, 'name', '长沙');
  
  if (result.success) {
    console.log('\nEnrichment完成！');
    console.log('统计信息：', result.coordinate_stats);
    
    console.log('\nEnrichment后的数据（前3个）：');
    result.enriched_data.slice(0, 3).forEach(item => {
      console.log({
        id: item.id,
        name: item.name,
        district: item.district,
        type: item.type,
        longitude: item.longitude,
        latitude: item.latitude,
        has_coordinates: item.has_coordinates,
        formatted_address: item.formatted_address
      });
    });
    
    // 统计各区域的建筑数量
    const districtStats = {};
    result.enriched_data.forEach(item => {
      if (item.has_coordinates) {
        districtStats[item.district] = (districtStats[item.district] || 0) + 1;
      }
    });
    
    console.log('\n各区域成功获取坐标的建筑数量：');
    Object.entries(districtStats).forEach(([district, count]) => {
      console.log(`${district}: ${count}个`);
    });
    
  } else {
    console.log('Enrichment失败：', result.message);
  }
  
  return result;
}

// 示例4：导出为CSV格式
async function example4_ExportCSV() {
  console.log('\n=== 示例4：导出为CSV格式 ===');
  
  const buildingNames = [
    '长沙国金中心', '华远国际中心', '长沙绿地中心T1栋', 
    '润和金融中心', '湖南商会大厦', '万坤图财富广场'
  ];
  
  const result = await getBatchCoordinates(buildingNames);
  
  if (result.success) {
    // 生成CSV格式数据
    const csvHeader = 'Name,Longitude,Latitude,Address,Status';
    const csvRows = result.results.map(item => {
      if (item.success) {
        return `"${item.name}",${item.longitude},${item.latitude},"${item.formatted_address}","Success"`;
      } else {
        return `"${item.name}",,,,"Failed: ${item.message}"`;
      }
    });
    
    const csvContent = [csvHeader, ...csvRows].join('\n');
    
    console.log('CSV格式数据：');
    console.log(csvContent);
    
    // 在实际应用中，您可以将csvContent保存到文件
    // fs.writeFileSync('building_coordinates.csv', csvContent, 'utf8');
    
  } else {
    console.log('查询失败，无法生成CSV');
  }
}

// 示例5：性能测试
async function example5_PerformanceTest() {
  console.log('\n=== 示例5：性能测试 ===');
  
  const testBuildings = [
    '长沙国金中心', '华远国际中心', '长沙绿地中心T1栋', 
    '润和金融中心', '湖南商会大厦'
  ];
  
  // 测试批量查询性能
  console.log('测试批量查询性能...');
  const startTime = Date.now();
  
  const batchResult = await getBatchCoordinates(testBuildings);
  
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  console.log(`批量查询耗时：${duration}ms`);
  console.log(`平均每个建筑：${(duration / testBuildings.length).toFixed(1)}ms`);
  
  if (batchResult.success) {
    console.log(`成功率：${(batchResult.success_count / batchResult.total * 100).toFixed(1)}%`);
  }
}

// 运行所有示例
export async function runBatchExamples() {
  try {
    await example1_SmallBatch();
    await example2_LargeBatch();
    await example3_EnrichData();
    await example4_ExportCSV();
    await example5_PerformanceTest();
    
    console.log('\n=== 所有批量查询示例执行完成 ===');
  } catch (error) {
    console.error('示例执行出错：', error);
  }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  runBatchExamples();
}
