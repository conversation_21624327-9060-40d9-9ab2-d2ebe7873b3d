/* 统一的页面布局样式 */

/* 通用页面容器 */
.page-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 统一的主要内容区域 */
.unified-main-content {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1.5fr 1fr; /* 统一比例：左1 中1.4 右1 */
  gap: 0.75vw; /* 响应式间隙，约20px在1600px屏幕 */
  padding: 1.25vw; /* 响应式内边距 */
  min-height: 0;
  overflow: hidden;
}

/* 统一的面板样式 */
.unified-panel {
  display: flex;
  flex-direction: column;
  min-height: 0;
  gap: 0.75vw; /* 响应式间隙，约12px在1600px屏幕 */
}

/* 左侧面板 */
.unified-left-panel {
  grid-column: 1;
}

/* 中央面板 */
.unified-center-panel {
  grid-column: 2;
}

/* 右侧面板 */
.unified-right-panel {
  grid-column: 3;
}

/* 统一的图表区域容器 */
.unified-chart-section {
  flex: 1;
  min-height: 0;
  /* background: rgba(0, 20, 50, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.2); */
  border-radius: 12px;
  overflow: hidden;
}

/* 特殊布局：分布饼图左右布局 */
.unified-distribution-pies {
  display: flex;
  gap: 0.5vw; /* 响应式间隙 */
}

.unified-pie-half {
  flex: 1;
  min-height: 0;
}

/* 响应式断点 */
@media (max-width: 1920px) {
  .unified-main-content {
    gap: 18px;
    padding: 18px;
  }
  
  .unified-panel {
    gap: 10px;
  }
  
  .unified-distribution-pies {
    gap: 8px;
  }
}

@media (max-width: 1600px) {
  .unified-main-content {
    gap: 15px;
    padding: 15px;
  }
  
  .unified-panel {
    gap: 8px;
  }
  
  .unified-distribution-pies {
    gap: 6px;
  }
}

@media (max-width: 1366px) {
  .unified-main-content {
    gap: 12px;
    padding: 12px;
  }
  
  .unified-panel {
    gap: 6px;
  }
  
  .unified-distribution-pies {
    gap: 4px;
  }
}

@media (max-width: 1024px) {
  .unified-main-content {
    grid-template-columns: 1fr; /* 小屏幕单列布局 */
    gap: 10px;
    padding: 10px;
  }
  
  .unified-left-panel,
  .unified-center-panel,
  .unified-right-panel {
    grid-column: 1;
  }
}

/* 特殊页面调整 */

/* Building页面特殊需求 */
.building-layout .unified-main-content {
  display: flex; /* 取消网格布局，改为flex布局 */
  max-height: calc(100vh - 280px); /* Building页面的高度限制 */
}

.building-layout .unified-left-panel {
  width: 22vw; /* 左侧固定宽度 */
  flex-shrink: 0;
}

.building-layout .unified-center-panel {
  width: 51vw; /* 中间固定宽度 */
  flex-shrink: 0;
}

.building-layout .unified-right-panel {
  width: 22vw; /* 右侧固定宽度 */
  flex-shrink: 0;
}

/* Index页面地图区域特殊处理 */
.index-layout .unified-center-panel {
  gap: 0; /* Index页面中央区域无间隙 */
}

/* District页面特殊调整 */
.district-layout .unified-main-content {
  grid-template-columns: 1fr 1.3fr 1fr; /* District页面保持原有比例 */
}
