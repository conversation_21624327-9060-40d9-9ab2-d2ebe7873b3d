<template>
  <div class="building-facilities-search">
    <div class="search-section">
      <h3>写字楼配套设施查询</h3>
      
      <!-- 搜索表单 -->
      <div class="search-form">
        <el-input
          v-model="buildingName"
          placeholder="请输入写字楼名称，如：长沙国金中心"
          style="width: 300px; margin-right: 10px;"
        />
        
        <el-select
          v-model="searchRadius"
          placeholder="搜索半径"
          style="width: 120px; margin-right: 10px;"
        >
          <el-option label="300米" :value="300" />
          <el-option label="500米" :value="500" />
          <el-option label="800米" :value="800" />
          <el-option label="1000米" :value="1000" />
        </el-select>
        
        <el-select
          v-model="facilityType"
          placeholder="设施类型"
          style="width: 150px; margin-right: 10px;"
        >
          <el-option label="全部设施" value="" />
          <el-option label="餐饮服务" value="restaurant" />
          <el-option label="购物服务" value="shopping" />
          <el-option label="银行金融" value="bank" />
          <el-option label="医疗服务" value="hospital" />
          <el-option label="地铁站" value="subway" />
          <el-option label="停车场" value="parking" />
        </el-select>
        
        <el-button 
          type="primary" 
          @click="searchFacilities"
          :loading="loading"
        >
          搜索
        </el-button>
      </div>
    </div>

    <!-- 搜索结果 -->
    <div v-if="searchResult" class="result-section">
      <!-- 建筑信息 -->
      <div class="building-info">
        <h4>建筑信息</h4>
        <div class="info-card">
          <p><strong>名称：</strong>{{ searchResult.building?.name }}</p>
          <p><strong>地址：</strong>{{ searchResult.building?.address }}</p>
          <p><strong>坐标：</strong>{{ searchResult.building?.longitude }}, {{ searchResult.building?.latitude }}</p>
        </div>
      </div>

      <!-- 配套设施统计 -->
      <div class="facilities-stats">
        <h4>配套设施统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">{{ searchResult.total_facilities }}</div>
            <div class="stat-label">总设施数</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ facilitiesStats.restaurant }}</div>
            <div class="stat-label">餐饮</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ facilitiesStats.shopping }}</div>
            <div class="stat-label">购物</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ facilitiesStats.bank }}</div>
            <div class="stat-label">银行</div>
          </div>
        </div>
      </div>

      <!-- 设施列表 -->
      <div class="facilities-list">
        <h4>设施详情 ({{ searchResult.facilities?.length }}个)</h4>
        <el-table 
          :data="searchResult.facilities" 
          style="width: 100%"
          max-height="400"
        >
          <el-table-column prop="name" label="名称" width="200" />
          <el-table-column prop="type" label="类型" width="150" />
          <el-table-column prop="address" label="地址" min-width="250" />
          <el-table-column prop="distance" label="距离(米)" width="100" />
          <el-table-column prop="tel" label="电话" width="150" />
        </el-table>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      <el-alert
        :title="errorMessage"
        type="error"
        :closable="false"
      />
    </div>
  </div>
</template>

<script>
import { getBuildingWithFacilities, getSpecificFacilities, getBuildingCoordinates } from '../net/addressHooks.js';

export default {
  name: 'BuildingFacilitiesSearch',
  data() {
    return {
      buildingName: '',
      searchRadius: 500,
      facilityType: '',
      loading: false,
      searchResult: null,
      errorMessage: '',
      facilitiesStats: {
        restaurant: 0,
        shopping: 0,
        bank: 0,
        hospital: 0
      }
    };
  },
  methods: {
    async searchFacilities() {
      if (!this.buildingName.trim()) {
        this.$message.warning('请输入写字楼名称');
        return;
      }

      this.loading = true;
      this.errorMessage = '';
      this.searchResult = null;

      try {
        const result = await getBuildingWithFacilities(
          this.buildingName,
          this.searchRadius,
          this.facilityType
        );

        if (result.success) {
          this.searchResult = result;
          await this.calculateFacilitiesStats();
          this.$message.success('查询成功');
        } else {
          this.errorMessage = result.message || '查询失败';
        }
      } catch (error) {
        this.errorMessage = '查询出错：' + error.message;
      } finally {
        this.loading = false;
      }
    },

    async calculateFacilitiesStats() {
      if (!this.searchResult?.building) return;

      const { longitude, latitude } = this.searchResult.building;
      const stats = { restaurant: 0, shopping: 0, bank: 0, hospital: 0 };

      // 分别查询各类设施数量
      for (const type of Object.keys(stats)) {
        try {
          const result = await getSpecificFacilities(longitude, latitude, type, this.searchRadius);
          stats[type] = result.success ? result.total : 0;
        } catch (error) {
          console.error(`查询${type}设施失败:`, error);
        }
      }

      this.facilitiesStats = stats;
    }
  }
};
</script>

<style scoped>
.building-facilities-search {
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-form {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.result-section {
  display: grid;
  gap: 20px;
}

.building-info, .facilities-stats, .facilities-list {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-top: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.error-message {
  margin-top: 20px;
}

h3, h4 {
  color: #333;
  margin-bottom: 10px;
}
</style>
