import { defineConfig } from 'vite'
import Vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver, NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import AutoImport from 'unplugin-auto-import/vite'
import { dirResolver, DirResolverHelper } from 'vite-auto-import-resolvers'
import Pages from "vite-plugin-pages"

export default defineConfig({
  resolve:{
    alias:{
      '~/':`${resolve(__dirname,'src')}/`,
      '@': resolve(__dirname, 'src')
    }
  },
  plugins: [
    Vue(),
    Pages({
      dirs:"src/views"  //views下文件自动生成路由
    }),
    DirResolverHelper(),
    Components({
      resolvers: [ElementPlusResolver(),NaiveUiResolver()]
    }),
    AutoImport({
      imports: ['vue', 'vue-router', 'pinia'],
      resolvers: [
        ElementPlusResolver(),
        dirR<PERSON><PERSON><PERSON>(),
        dir<PERSON><PERSON><PERSON>ver({
          target:'src/stores', // 目标目录，默认为 'src/composables'
          suffix: "Store" // 强制后缀为 Store
        }),
      ]
    })
  ],
  server: {
    port: 8080,
    host: '0.0.0.0', // 允许外部访问
    proxy: {
      '/api/maps': {
        target: 'https://geo.datav.aliyun.com',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/api\/maps/, '/areas_v3/bound'),
        headers: {
          'Referer': 'http://localhost:8080',
          'Origin': 'http://localhost:8080'
        },
      }
    }
  },
  // build: {
  //   target: 'esnext',
  //   chunkSizeWarningLimit: 1000,
  //   rollupOptions: {
  //     output: {
  //       manualChunks(id) {
  //         if (id.includes('echarts')) {
  //           return 'echarts'
  //         }
  //       }
  //     }
  //   }
  // }
})
