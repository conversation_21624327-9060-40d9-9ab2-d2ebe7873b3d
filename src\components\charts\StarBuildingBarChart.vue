<template>
  <div class="star-building-bar-chart">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle :title="selectedRegion.level === 'city' ? '各区星级楼宇分布' : '各街道星级楼宇分布'" />
        <div ref="barChartRef" class="bar-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from 'vue';
import * as echarts from 'echarts';
import { useColors } from '@/composables/useColors';
import ChartTitle from '@/components/common/ChartTitle.vue';
const { colors, currentTheme, toggleTheme, getCSSVars } = useColors()

// 接收父组件传递的选中区域信息和星级楼宇数据
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: '长沙市',
      level: 'city',
      areaData: null
    })
  },
  buildingLevelData: {
    type: Object,
    default: null
  }
});

// 柱状图引用
const barChartRef = ref(null);
let barChart = null;


// 动态星级楼宇数据
const starBuildingData = computed(() => {
  // 优先使用传入的真实数据
  if (props.buildingLevelData) {
    return props.buildingLevelData;
  }

  // 如果没有传入数据，使用静态数据作为后备
  if (props.selectedRegion.level === 'city') {
    return cityStarBuildingData;
  } else {
    return districtStarBuildingData[props.selectedRegion.regionName] || cityStarBuildingData;
  }
});

// 初始化柱状图
const initBarChart = () => {
  if (!barChartRef.value) return;

  barChart = echarts.init(barChartRef.value);

  // 准备图表数据
  const currentData = starBuildingData.value;
  console.log('StarBuildingBarChart - 当前数据:', currentData);
  console.log('StarBuildingBarChart - 选中区域:', props.selectedRegion);
  console.log('StarBuildingBarChart - 传入的buildingLevelData:', props.buildingLevelData);

  const categories = props.selectedRegion.level === 'city' ? currentData.districts : currentData.streets;
  const starLevels = currentData.starLevels;
  const data = currentData.data;

  console.log('StarBuildingBarChart - categories:', categories);
  console.log('StarBuildingBarChart - starLevels:', starLevels);
  console.log('StarBuildingBarChart - data:', data);

  // 构建系列数据 - 改为多根柱子，子弹头圆角，颜色渐变
  const series = starLevels.map((level, index) => {
    // 定义渐变色配置
    const gradientColors = [
      {
        // 4星标准级 - 橙红渐变
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#ff7043' },
          { offset: 1, color: '#d84315' }
        ]
      },
      {
        // 5星甲级 - 橙黄渐变
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#ffa726' },
          { offset: 1, color: '#f57c00' }
        ]
      },
      {
        // 6星超甲级 - 绿色渐变
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#66bb6a' },
          { offset: 1, color: '#388e3c' }
        ]
      },
      {
        // 7星顶级 - 蓝色渐变
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#4fc3f7' },
          { offset: 1, color: '#0277bd' }
        ]
      }
    ];

    return {
      name: level,
      type: 'bar',
      // 移除stack，让柱子并排显示
      barWidth: '15%', // 控制柱子宽度
      data: categories.map(category => {
        // 安全检查，避免访问不存在的数据
        return (data[category] && data[category][level]) ? data[category][level] : 0;
      }),
      itemStyle: {
        color: gradientColors[index],
        // 子弹头圆角效果
        borderRadius: [20, 20, 0, 0], // 顶部圆角
        shadowBlur: 8,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowOffsetY: 2
      },
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 15,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      // 添加动画效果
      animationDelay: (idx) => idx * 100
    };
  });

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 20, 50, 0.8)',
      borderColor: '#4fc3f7',
      textStyle: { color: '#fff' },
      formatter: function(params) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].axisValue}</div>`;
        params.forEach(param => {
          result += `<div>${param.marker} ${param.seriesName}: ${param.value}栋</div>`;
        });
        return result;
      }
    },
    legend: {
      data: starLevels,
      textStyle: {
        color: '#e3f2fd',
        fontSize: 14  // 增加2px：10 + 2 = 12
      },
      top: 10,
      itemWidth: 12,
      itemHeight: 8
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '35%',  // 增加10%来增加图例与图表之间的距离（约20px）
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        color: '#e3f2fd',
        fontSize: 13,  // 增加2px：9 + 2 = 11
        rotate: 45
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(79, 195, 247, 0.3)'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '数量(栋)',
      nameTextStyle: {
        color: '#e3f2fd',
        fontSize: 14  // 增加2px：10 + 2 = 12
      },
      axisLabel: {
        color: '#e3f2fd',
        fontSize: 13  // 增加2px：9 + 2 = 11
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(79, 195, 247, 0.3)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(79, 195, 247, 0.1)'
        }
      }
    },
    series: series
  };

  barChart.setOption(option);
};

// 处理窗口大小变化
const handleResize = () => {
  if (barChart) {
    barChart.resize();
  }
};

// 监听区域变化，重新初始化柱状图
watch(() => props.selectedRegion, () => {
  // 重新初始化柱状图
  if (barChart) {
    initBarChart();
  }
}, { deep: true });

// 监听区域变化，重新初始化柱状图
watch(() => props.selectedRegion, () => {
  // 重新初始化柱状图
  if (barChart) {
    initBarChart();
  }
}, { deep: true });

// 监听星级楼宇数据变化，重新初始化柱状图
watch(() => props.buildingLevelData, () => {
  console.log('StarBuildingBarChart - buildingLevelData数据变化:', props.buildingLevelData);
  // 重新初始化柱状图
  if (barChart) {
    initBarChart();
  }
}, { deep: true });

onMounted(() => {
  // 初始化图表
  nextTick(() => {
    initBarChart();
  });

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (barChart) {
    barChart.dispose();
  }
});
</script>

<style scoped lang="less">
.star-building-bar-chart {
  height: 100%;
}

.border-box {
  height: 100%;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 原有的标题样式已移至ChartTitle组件 */

.bar-chart {
  flex: 1;
  width: 100%;
  min-height: 200px;
}
</style>
