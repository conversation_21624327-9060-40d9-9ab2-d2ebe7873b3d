<template>
  <div class="enterprise-risk-warning">
    <div class="panel-title-with-filter">
        <ChartTitle title="企业风险预警" />
      <div class="risk-filter">
        <select v-model="selectedRiskFilter" class="filter-select" @change="filterRiskWarnings">
          <option value="all">全部风险</option>
          <option value="拖欠租金">拖欠租金</option>
          <option value="租约到期">租约到期</option>
          <option value="经营风险">经营风险</option>
          <option value="迁出风险">迁出风险</option>
        </select>
      </div>
    </div>
    <div class="warning-list" @mouseenter="stopRiskScroll" @mouseleave="startRiskScroll">
      <div v-for="warning in filteredRiskWarnings" :key="warning.id" class="warning-item">
        <div class="warning-content">
          <div class="warning-company">{{ warning.companyName }}</div>
          <div class="risk-items">
            <div v-for="risk in warning.risks" :key="risk.type" class="risk-item">
              <span class="risk-icon" :style="{ color: risk.color }">{{ risk.icon }}</span>
              <span class="risk-text" :style="{ color: risk.color }">{{ risk.type }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import ChartTitle from '@/components/common/ChartTitle.vue';

// 风险筛选状态
const selectedRiskFilter = ref('all');
const filteredRiskWarnings = ref([]);

// 企业风险预警数据
const enterpriseRiskWarnings = ref([
  {
    id: 1,
    companyName: "华为技术有限公司",
    risks: [
      { type: "租约到期", level: "warning", icon: "⚠️", color: "#FFA500" },
      { type: "拖欠租金", level: "danger", icon: "💰", color: "#FF4444" }
    ]
  },
  {
    id: 2,
    companyName: "腾讯科技有限公司",
    risks: [{ type: "经营风险", level: "danger", icon: "❗", color: "#FF4444" }]
  },
  {
    id: 3,
    companyName: "阿里巴巴集团",
    risks: [
      { type: "迁出风险", level: "warning", icon: "🚪", color: "#FFA500" }
    ]
  },
  {
    id: 4,
    companyName: "百度在线网络",
    risks: [
      { type: "拖欠租金", level: "danger", icon: "💰", color: "#FF4444" },
      { type: "租约到期", level: "warning", icon: "⚠️", color: "#FFA500" }
    ]
  },
  {
    id: 5,
    companyName: "字节跳动科技",
    risks: [
      { type: "经营风险", level: "danger", icon: "❗", color: "#FF4444" }
    ]
  },
  {
    id: 6,
    companyName: "美团点评",
    risks: [
      { type: "迁出风险", level: "warning", icon: "🚪", color: "#FFA500" },
      { type: "租约到期", level: "warning", icon: "⚠️", color: "#FFA500" }
    ]
  },
  {
    id: 7,
    companyName: "滴滴出行科技",
    risks: [
      { type: "拖欠租金", level: "danger", icon: "💰", color: "#FF4444" }
    ]
  },
  {
    id: 8,
    companyName: "小米科技",
    risks: [
      { type: "经营风险", level: "danger", icon: "❗", color: "#FF4444" },
      { type: "迁出风险", level: "warning", icon: "🚪", color: "#FFA500" }
    ]
  }
]);

// 自动滚动相关
let riskScrollInterval = null;

// 筛选风险预警数据
const filterRiskWarnings = () => {
  console.log("筛选条件:", selectedRiskFilter.value);
  
  if (!enterpriseRiskWarnings.value || !Array.isArray(enterpriseRiskWarnings.value)) {
    filteredRiskWarnings.value = [];
    return;
  }

  if (selectedRiskFilter.value === "all") {
    filteredRiskWarnings.value = [...enterpriseRiskWarnings.value];
  } else {
    filteredRiskWarnings.value = enterpriseRiskWarnings.value.filter(
      warning => {
        if (!warning || !warning.risks || !Array.isArray(warning.risks)) {
          return false;
        }
        return warning.risks.some(
          risk => risk && risk.type === selectedRiskFilter.value
        );
      }
    );
  }
  
  console.log("筛选后数据:", filteredRiskWarnings.value);
};

// 启动自动滚动
const startRiskScroll = () => {
  const container = document.querySelector('.enterprise-risk-warning .warning-list');
  if (!container) return;

  riskScrollInterval = setInterval(() => {
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;
    const currentScrollTop = container.scrollTop;

    if (currentScrollTop + clientHeight >= scrollHeight - 5) {
      container.scrollTop = 0;
    } else {
      container.scrollTop += 1;
    }
  }, 50);
};

// 停止自动滚动
const stopRiskScroll = () => {
  if (riskScrollInterval) {
    clearInterval(riskScrollInterval);
    riskScrollInterval = null;
  }
};

onMounted(() => {
  // 初始化筛选数据
  filterRiskWarnings();
  
  // 延迟启动自动滚动
  setTimeout(() => {
    startRiskScroll();
  }, 2000);
});

onUnmounted(() => {
  stopRiskScroll();
});
</script>

<style scoped lang="less">
/* 企业风险预警 */
.enterprise-risk-warning {
  border-radius: 12px;
  padding: 15px;
  height: 100%;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #4fc3f7;
  margin-bottom: 15px;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
  text-align: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.panel-title-with-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title-with-filter .panel-title {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
  text-align: left;
}

.risk-filter {
  display: flex;
  align-items: center;
}

.filter-select {
  background: rgba(0, 30, 60, 0.8);
  border: 1px solid rgba(79, 195, 247, 0.4);
  border-radius: 6px;
  color: #4fc3f7;
  padding: 4px 8px;
  font-size: 12px;
  outline: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-select:hover {
  border-color: rgba(79, 195, 247, 0.6);
  background: rgba(0, 30, 60, 0.9);
}

.filter-select:focus {
  border-color: #4fc3f7;
  box-shadow: 0 0 5px rgba(79, 195, 247, 0.3);
}

.filter-select option {
  background: rgba(0, 30, 60, 0.9);
  color: #4fc3f7;
  padding: 4px;
}

.warning-list {
  max-height: 270px;
  overflow-y: auto;

  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none;
  }

  /* Firefox */
  scrollbar-width: none;

  /* IE and Edge */
  -ms-overflow-style: none;
}

.warning-item {
  padding: 10px 12px;
  background: rgba(0, 30, 60, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(79, 195, 247, 0.2);
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.warning-item:hover {
  background: rgba(0, 30, 60, 0.8);
  transform: translateX(3px);
  border-color: rgba(79, 195, 247, 0.4);
}

.warning-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.warning-company {
  font-weight: bold;
  color: #4fc3f7;
  font-size: 13px;
  flex-shrink: 0;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.risk-items {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: flex-end;
  flex: 1;
}

.risk-item {
  display: flex;
  align-items: center;
  gap: 3px;
  background: rgba(0, 0, 0, 0.3);
  padding: 3px 6px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.risk-icon {
  font-size: 10px;
}

.risk-text {
  font-size: 10px;
  font-weight: bold;
  white-space: nowrap;
}
</style>
