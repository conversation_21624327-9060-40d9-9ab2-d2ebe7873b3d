<template>
  <div class="tools-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>数据工具</h1>
      <p>提供写字楼数据查询和分析工具</p>
    </div>

    <!-- 工具导航 -->
    <div class="tools-nav">
      <div class="nav-cards">
        <div 
          class="nav-card"
          :class="{ active: activeTab === 'batch' }"
          @click="activeTab = 'batch'"
        >
          <div class="card-icon">
            <i class="el-icon-location"></i>
          </div>
          <div class="card-content">
            <h3>批量查询坐标</h3>
            <p>批量获取写字楼经纬度坐标</p>
          </div>
        </div>

        <div 
          class="nav-card"
          :class="{ active: activeTab === 'facilities' }"
          @click="activeTab = 'facilities'"
        >
          <div class="card-icon">
            <i class="el-icon-search"></i>
          </div>
          <div class="card-content">
            <h3>配套设施查询</h3>
            <p>查询写字楼周边配套设施</p>
          </div>
        </div>

        <div 
          class="nav-card"
          :class="{ active: activeTab === 'analysis' }"
          @click="activeTab = 'analysis'"
        >
          <div class="card-icon">
            <i class="el-icon-data-analysis"></i>
          </div>
          <div class="card-content">
            <h3>数据分析</h3>
            <p>写字楼数据统计分析</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具内容区域 -->
    <div class="tools-content">
      <!-- 批量查询坐标 -->
      <div v-show="activeTab === 'batch'" class="tool-panel">
        <BatchCoordinatesQuery />
      </div>

      <!-- 配套设施查询 -->
      <div v-show="activeTab === 'facilities'" class="tool-panel">
        <BuildingFacilitiesSearch />
      </div>

      <!-- 数据分析 -->
      <div v-show="activeTab === 'analysis'" class="tool-panel">
        <div class="analysis-panel">
          <h3>数据统计分析</h3>
          
          <!-- 现有数据统计 -->
          <div class="stats-section">
            <h4>现有数据统计</h4>
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-number">{{ totalBuildings }}</div>
                <div class="stat-label">总建筑数</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ buildingsWithCoords }}</div>
                <div class="stat-label">有坐标建筑</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ coordCoverage }}%</div>
                <div class="stat-label">坐标覆盖率</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ districtCount }}</div>
                <div class="stat-label">覆盖区域</div>
              </div>
            </div>
          </div>

          <!-- 区域分布 -->
          <div class="district-section">
            <h4>区域分布</h4>
            <el-table :data="districtStats" style="width: 100%">
              <el-table-column prop="district" label="区域" width="150" />
              <el-table-column prop="count" label="建筑数量" width="120" />
              <el-table-column prop="percentage" label="占比" width="100">
                <template slot-scope="scope">
                  {{ scope.row.percentage }}%
                </template>
              </el-table-column>
              <el-table-column label="进度">
                <template slot-scope="scope">
                  <el-progress 
                    :percentage="scope.row.percentage" 
                    :stroke-width="8"
                    :show-text="false"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 操作按钮 -->
          <div class="analysis-actions">
            <el-button @click="refreshStats" type="primary">
              刷新统计
            </el-button>
            <el-button @click="exportStats" type="success">
              导出统计报告
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速访问链接 -->
    <div class="quick-links">
      <h4>快速访问</h4>
      <div class="links-grid">
        <router-link to="/" class="quick-link">
          <i class="el-icon-house"></i>
          <span>返回首页</span>
        </router-link>
        <router-link to="/district" class="quick-link">
          <i class="el-icon-location-outline"></i>
          <span>区级页面</span>
        </router-link>
        <router-link to="/building" class="quick-link">
          <i class="el-icon-office-building"></i>
          <span>楼宇页面</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import BatchCoordinatesQuery from '@/components/BatchCoordinatesQuery.vue';
import BuildingFacilitiesSearch from '@/components/BuildingFacilitiesSearch.vue';
// 原始地址数据
const originalAddressData = [
  { "name": "德思勤城市广场", "longitude": 113.05902, "latitude": 28.11799 },
  { "name": "华尔街中心", "longitude": 112.986335, "latitude": 28.215477 },
  { "name": "华远国际中心", "longitude": 112.969975, "latitude": 28.189106 },
  { "name": "汇景发展环球中心", "longitude": 112.969326, "latitude": 28.174152 },
  { "name": "长沙佳兆业广场", "longitude": 112.999634, "latitude": 28.194831 }
  // 实际项目中应该包含完整数据
];

export default {
  name: 'ToolsPage',
  components: {
    BatchCoordinatesQuery,
    BuildingFacilitiesSearch
  },
  data() {
    return {
      activeTab: 'batch',
      totalBuildings: 0,
      buildingsWithCoords: 0,
      districtStats: []
    };
  },
  computed: {
    coordCoverage() {
      return this.totalBuildings > 0 ? 
        ((this.buildingsWithCoords / this.totalBuildings) * 100).toFixed(1) : 0;
    },
    
    districtCount() {
      return this.districtStats.length;
    }
  },
  
  mounted() {
    this.calculateStats();
  },
  
  methods: {
    calculateStats() {
      // 计算现有数据统计
      this.totalBuildings = originalAddressData.length;
      this.buildingsWithCoords = originalAddressData.filter(item =>
        item.longitude && item.latitude
      ).length;
      
      // 模拟区域分布统计（实际项目中应该从真实数据计算）
      const districts = [
        { district: '芙蓉区', count: 45 },
        { district: '天心区', count: 38 },
        { district: '岳麓区', count: 32 },
        { district: '开福区', count: 28 },
        { district: '雨花区', count: 25 },
        { district: '望城区', count: 15 },
        { district: '长沙县', count: 12 },
        { district: '浏阳市', count: 8 },
        { district: '宁乡市', count: 6 }
      ];
      
      const total = districts.reduce((sum, item) => sum + item.count, 0);
      this.districtStats = districts.map(item => ({
        ...item,
        percentage: ((item.count / total) * 100).toFixed(1)
      }));
    },
    
    refreshStats() {
      this.calculateStats();
      this.$message.success('统计数据已刷新');
    },
    
    exportStats() {
      const statsData = {
        summary: {
          totalBuildings: this.totalBuildings,
          buildingsWithCoords: this.buildingsWithCoords,
          coordCoverage: this.coordCoverage,
          districtCount: this.districtCount
        },
        districtStats: this.districtStats,
        exportTime: new Date().toLocaleString()
      };
      
      const jsonContent = JSON.stringify(statsData, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const link = document.createElement('a');
      
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `building_stats_${new Date().getTime()}.json`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
      
      this.$message.success('统计报告已导出');
    }
  }
};
</script>

<style scoped>
.tools-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.page-header {
  text-align: center;
  color: white;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 300;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.tools-nav {
  margin-bottom: 30px;
}

.nav-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.nav-card.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.card-icon {
  font-size: 2.5rem;
  color: #667eea;
  min-width: 60px;
}

.nav-card.active .card-icon {
  color: white;
}

.card-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.card-content p {
  margin: 0;
  opacity: 0.8;
  font-size: 0.95rem;
}

.tools-content {
  max-width: 1400px;
  margin: 0 auto;
}

.tool-panel {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.analysis-panel {
  padding: 30px;
}

.stats-section {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.stat-card {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 8px;
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 0.95rem;
}

.district-section {
  margin-bottom: 30px;
}

.analysis-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.quick-links {
  max-width: 1200px;
  margin: 30px auto 0;
  padding: 25px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.quick-links h4 {
  color: white;
  text-align: center;
  margin-bottom: 20px;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.quick-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.quick-link:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.quick-link i {
  font-size: 1.8rem;
  margin-bottom: 8px;
}

h3, h4 {
  color: #333;
  margin-bottom: 15px;
}
</style>
