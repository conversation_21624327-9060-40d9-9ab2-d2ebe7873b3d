# Vue 3 + Vite 
# 目录结构
# src/
# ├── net/            # API 请求封装
# ├── assets/         # 静态资源
# ├── components/     # 全局组件
# │   ├── charts/     # 图表组件
# │   ├── dashboard/  # 大屏专用组件
# │   └── widgets/    # 通用小组件
# ├── composables/    # 组合式函数
# ├── stores/         # 状态管理
# ├── styles/         # 全局样式
# ├── utils/          # 工具函数
# └── views/          # 视图组件

# vite.config.js已配置按需引入、Pages路由
# 状态管理文件放在stores文件夹里面，文件命名统一使用xxxStore

# 路由配置说明：
# views下文件自动生成路由，无需另外配置，path与文件名一致
# 初始页固定为index.vue
# 单个页面如需另外配置，可在vue文件中加入以下内容：
# <route>
#    {
#        path: '/xxx',
#        name: 'XXX',
#        meta: {
#            title: "XXX",
#        },
#    }
</route>

# 调用后台接口使用方式(默认post，若为get,需在data中传入method:'get')
#  function userRegister(ctx, params) {
#        const data = {
#           url: '/xxx/openapi/xxx.action',
#           params: {
#                 ...params
#            },
#            callback: (res) => {
#           }
#        }
#        return request(data)
#    }

# pinia说明
# 创建名为xxxStrore.js文件
# 文件内容如下：
# export const xxxStore = defineStore('abc', () => {
#   const x = ref(0);
#   function xxxFunction() {
#     //这里写函数内容
#   }
#   return { xxx, xxxFunction }
# })
# 使用时直接调用abc.xxxFunction,直接用abc.xxx获取store中变量

