<template>
  <!-- 区级页面左侧：返回按钮和区域信息 -->
  <div class="back-section">
    <div class="back-button" @click="goBack">
      <div class="back-icon-wrapper">
        <img src="@/assets/icon/goback.svg" class="back-icon" alt="返回" />
        <span class="back-text">返回</span>
      </div>
    </div>
    <div class="district-info">
      <div class="district-name">{{ regionName }}</div>
      <div class="district-level">区级数据展示</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  route: Object,
  router: Object
});

// 获取区域名称
const regionName = computed(() => {
  return props.route.params.region || props.route.query.regionName || '芙蓉区';
});

// 返回首页
const goBack = () => {
  props.router.push('/');
};
</script>

<style scoped lang="less">
.back-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  transform: scale(1.05);
}

.back-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 100px;
  height: auto;
  border: none;
  outline: none;
  background: transparent;
}

.back-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 14px;
  font-weight: 600;
  text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
  pointer-events: none;
}

.district-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.district-name {
  color: #4fc3f7;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 1px;
}

.district-level {
  color: #81d4fa;
  font-size: 12px;
  opacity: 0.8;
}
</style>
