<template>
  <div class="building-info-stats">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="楼宇信息统计" />
        <div class="stats-grid">
          <div v-for="stat in buildingStats" :key="stat.label" class="stat-item">
            <div class="stat-icon">
              <img :src="dataIconUrl" alt="数据图标" class="icon-image" />
              <div class="stat-value">{{ stat.value }}</div>
            </div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import dataIconUrl from '@/assets/icon/dataicon.svg';
import ChartTitle from '@/components/common/ChartTitle.vue';

// 楼宇信息统计数据
const buildingStats = ref([
  { label: '楼宇总数量', value: '1,268', unit: '栋' },
  { label: '总建筑面积', value: '2,856.8', unit: '万㎡' },
  { label: '总招商面积', value: '2,134.5', unit: '万㎡' },
  { label: '入驻企业数量', value: '8,945', unit: '家' }
]);

// 更新楼宇统计数据
const updateBuildingStats = () => {
  buildingStats.value.forEach(stat => {
    const baseValue = parseFloat(stat.value.replace(/,/g, ''));
    let variation = 0;

    if (stat.label === '楼宇总数量') {
      variation = Math.floor(Math.random() * 10 - 5); // -5到+5的变化
      stat.value = (baseValue + variation).toLocaleString();
    } else if (stat.label === '入驻企业数量') {
      variation = Math.floor(Math.random() * 50 - 25); // -25到+25的变化
      stat.value = (baseValue + variation).toLocaleString();
    } else {
      variation = parseFloat((Math.random() * 20 - 10).toFixed(1)); // -10到+10的变化
      stat.value = (baseValue + variation).toLocaleString();
    }
  });
};

let updateInterval = null;

onMounted(() => {
  // 数据更新逻辑
  updateInterval = setInterval(() => {
    updateBuildingStats();
  }, 7000); // 每7秒更新一次数据
});

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval);
  }
});
</script>

<style scoped lang="less">
.building-info-stats {
  height: 100%;
}

.border-box {
  height: 100%;
  background-size: 100% 100%;
  padding: 20px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 原有的标题样式已移至ChartTitle组件 */

.stats-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: center;
  padding: 10px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
}

.stat-icon {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 8px;
}

.icon-image {
  width: 80px;
  height: 80px;
  animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.stat-value {
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  font-weight: bold;
  color: #4fc3f7;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
  white-space: nowrap;
  z-index: 2;
}

.stat-label {
  color: #e3f2fd;
  font-size: 11px;
  font-weight: 500;
  opacity: 0.9;
  letter-spacing: 0.5px;
  line-height: 1.2;
}
</style>
