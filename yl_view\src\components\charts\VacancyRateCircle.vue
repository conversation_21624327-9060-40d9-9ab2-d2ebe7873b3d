<template>
  <div class="vacancy-rate-liquidfill">
    <div class="chart-container">
      <div class="round-icon-wrapper">
        <img src="@/assets/icon/roundIcon.svg" class="round-icon-bg" alt="background" />
        <div ref="liquidfillChartRef" class="liquidfill-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import * as echarts from 'echarts';
import 'echarts-liquidfill';

// 图表引用
const liquidfillChartRef = ref(null);
let liquidfillChart = null;

// 空置率数据
const vacancyRate = ref(45);

// 初始化水球图
const initLiquidfillChart = () => {
  if (!liquidfillChartRef.value) return;

  liquidfillChart = echarts.init(liquidfillChartRef.value);

  // 创建三层水波重叠效果
  const baseValue = vacancyRate.value / 100;

  const option = {
    series: [
      // 第一层水波 - 主要水位
      {
        type: 'liquidFill',
        data: [baseValue], // 主要水位
        radius: '85%',
        center: ['50%', '50%'],
        color: [
          {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: '#4fc3f7' },
              { offset: 0.5, color: '#29b6f6' },
              { offset: 1, color: '#0277bd' }
            ]
          }
        ],
        backgroundStyle: {
          color: 'rgba(79, 195, 247, 0.1)',
          borderWidth: 2,
          borderColor: 'rgba(79, 195, 247, 0.3)',
          shadowColor: 'rgba(79, 195, 247, 0.3)',
          shadowBlur: 20
        },
        outline: {
          show: true,
          borderDistance: 5,
          itemStyle: {
            borderWidth: 2,
            borderColor: '#4fc3f7',
            shadowBlur: 20,
            shadowColor: 'rgba(79, 195, 247, 0.5)'
          }
        },
        label: {
          show: true,
          color: '#ffffff',
          insideColor: '#ffffff',
          fontSize: 18,
          fontWeight: 'bold',
          formatter: function(param) {
            return Math.round(vacancyRate.value) + '%\n空置率';
          },
          textStyle: {
            textShadowColor: 'rgba(0, 0, 0, 0.3)',
            textShadowBlur: 5,
            lineHeight: 22
          }
        },
        // 第一层水波动画配置
        amplitude: 8,
        waveLength: '80%',
        period: 2000,
        direction: 'right',
        waveAnimation: true,
        animationEasing: 'quadraticInOut',
        animationDuration: 2000
      },
      // 第二层水波 - 稍低水位，不同波长
      {
        type: 'liquidFill',
        data: [Math.max(0, baseValue - 0.05)], // 稍低的水位
        radius: '85%',
        center: ['50%', '50%'],
        color: [
          {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(79, 195, 247, 0.6)' },
              { offset: 0.5, color: 'rgba(41, 182, 246, 0.6)' },
              { offset: 1, color: 'rgba(2, 119, 189, 0.6)' }
            ]
          }
        ],
        backgroundStyle: {
          color: 'transparent' // 透明背景，避免重复
        },
        outline: {
          show: false // 不显示边框，避免重复
        },
        label: {
          show: false // 不显示标签，避免重复
        },
        // 第二层水波动画配置 - 不同的波长和周期
        amplitude: 6,
        waveLength: '60%',
        period: 2500,
        direction: 'left', // 反向流动
        waveAnimation: true,
        animationEasing: 'quadraticInOut',
        animationDuration: 2500
      },
      // 第三层水波 - 更低水位，更细的波纹
      {
        type: 'liquidFill',
        data: [Math.max(0, baseValue - 0.1)], // 更低的水位
        radius: '85%',
        center: ['50%', '50%'],
        color: [
          {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(79, 195, 247, 0.4)' },
              { offset: 0.5, color: 'rgba(41, 182, 246, 0.4)' },
              { offset: 1, color: 'rgba(2, 119, 189, 0.4)' }
            ]
          }
        ],
        backgroundStyle: {
          color: 'transparent' // 透明背景
        },
        outline: {
          show: false // 不显示边框
        },
        label: {
          show: false // 不显示标签
        },
        // 第三层水波动画配置 - 更快的波动
        amplitude: 4,
        waveLength: '40%',
        period: 1500,
        direction: 'right',
        waveAnimation: true,
        animationEasing: 'quadraticInOut',
        animationDuration: 1500
      }
    ]
  };

  liquidfillChart.setOption(option);
};

// 更新水球图数据 - 更新三层水波
const updateChart = () => {
  if (liquidfillChart) {
    const baseValue = vacancyRate.value / 100;
    const option = {
      series: [
        {
          data: [baseValue] // 第一层主要水位
        },
        {
          data: [Math.max(0, baseValue - 0.05)] // 第二层稍低水位
        },
        {
          data: [Math.max(0, baseValue - 0.1)] // 第三层更低水位
        }
      ]
    };
    liquidfillChart.setOption(option);
  }
};

// 模拟数据更新
const updateVacancyRate = () => {
  const change = Math.floor(Math.random() * 6) - 3; // -3 到 +3 的随机变化
  vacancyRate.value = Math.max(0, Math.min(100, vacancyRate.value + change));
  updateChart();
};

// 处理窗口大小变化
const handleResize = () => {
  if (liquidfillChart) {
    liquidfillChart.resize();
  }
};

let updateInterval = null;

onMounted(() => {
  nextTick(() => {
    initLiquidfillChart();
  });

  // 数据更新逻辑
  updateInterval = setInterval(() => {
    updateVacancyRate();
  }, 8000); // 每8秒更新一次数据

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval);
  }
  window.removeEventListener('resize', handleResize);
  if (liquidfillChart) {
    liquidfillChart.dispose();
  }
});
</script>

<style scoped lang="less">
.vacancy-rate-liquidfill {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.chart-container {
  position: relative;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.round-icon-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.round-icon-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
  object-fit: contain;
}

.liquidfill-chart {
  position: relative;
  width: 80%;
  height: 80%;
  z-index: 2;
}



/* 添加整体发光效果 */
.chart-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 130px;
  height: 130px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(79, 195, 247, 0.1) 0%, transparent 70%);
  animation: liquidGlow 4s ease-in-out infinite alternate;
  z-index: 0;
}

@keyframes liquidGlow {
  0% {
    box-shadow:
      0 0 20px rgba(79, 195, 247, 0.2),
      0 0 40px rgba(79, 195, 247, 0.1);
  }
  100% {
    box-shadow:
      0 0 30px rgba(79, 195, 247, 0.4),
      0 0 60px rgba(79, 195, 247, 0.2);
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .chart-container {
    width: 100px;
    height: 100px;
  }

  .liquidfill-chart {
    width: 80px;
    height: 80px;
  }

  .chart-container::before {
    width: 90px;
    height: 90px;
  }
}
</style>
