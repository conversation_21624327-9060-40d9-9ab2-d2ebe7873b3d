<template>
  <div class="tools-navigation">
    <!-- 浮动工具按钮 -->
    <div class="floating-tools-btn" @click="showToolsMenu = !showToolsMenu">
      <i class="el-icon-setting"></i>
      <span>工具</span>
    </div>

    <!-- 工具菜单 -->
    <transition name="tools-menu">
      <div v-if="showToolsMenu" class="tools-menu">
        <div class="menu-header">
          <h4>数据工具</h4>
          <i class="el-icon-close" @click="showToolsMenu = false"></i>
        </div>
        
        <div class="menu-items">
          <router-link 
            to="/tools" 
            class="menu-item"
            @click="showToolsMenu = false"
          >
            <i class="el-icon-data-analysis"></i>
            <div class="item-content">
              <div class="item-title">工具中心</div>
              <div class="item-desc">数据工具集合页面</div>
            </div>
          </router-link>

          <router-link 
            to="/batch-query" 
            class="menu-item"
            @click="showToolsMenu = false"
          >
            <i class="el-icon-location"></i>
            <div class="item-content">
              <div class="item-title">批量查询坐标</div>
              <div class="item-desc">批量获取写字楼经纬度</div>
            </div>
          </router-link>

          <router-link 
            to="/facilities-search" 
            class="menu-item"
            @click="showToolsMenu = false"
          >
            <i class="el-icon-search"></i>
            <div class="item-content">
              <div class="item-title">配套设施查询</div>
              <div class="item-desc">查询周边配套设施</div>
            </div>
          </router-link>

          <div class="menu-divider"></div>

          <router-link 
            to="/" 
            class="menu-item"
            @click="showToolsMenu = false"
          >
            <i class="el-icon-house"></i>
            <div class="item-content">
              <div class="item-title">返回首页</div>
              <div class="item-desc">回到主页面</div>
            </div>
          </router-link>
        </div>
      </div>
    </transition>

    <!-- 遮罩层 -->
    <div 
      v-if="showToolsMenu" 
      class="menu-overlay"
      @click="showToolsMenu = false"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'ToolsNavigation',
  data() {
    return {
      showToolsMenu: false
    };
  },
  mounted() {
    // 监听ESC键关闭菜单
    document.addEventListener('keydown', this.handleKeydown);
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown);
  },
  methods: {
    handleKeydown(event) {
      if (event.key === 'Escape') {
        this.showToolsMenu = false;
      }
    }
  }
};
</script>

<style scoped>
.tools-navigation {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
}

.floating-tools-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.floating-tools-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.floating-tools-btn i {
  font-size: 1.2rem;
}

.tools-menu {
  position: absolute;
  top: 60px;
  right: 0;
  width: 320px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border: 1px solid #e1e5e9;
}

.menu-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.menu-header i {
  cursor: pointer;
  font-size: 1.2rem;
  padding: 5px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.menu-header i:hover {
  background: rgba(255, 255, 255, 0.2);
}

.menu-items {
  padding: 10px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #333;
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.menu-item:hover {
  background: #f8f9fa;
  border-left-color: #667eea;
}

.menu-item.router-link-active {
  background: #f0f4ff;
  border-left-color: #667eea;
  color: #667eea;
}

.menu-item i {
  font-size: 1.3rem;
  margin-right: 15px;
  color: #667eea;
  min-width: 20px;
}

.item-content {
  flex: 1;
}

.item-title {
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 2px;
}

.item-desc {
  font-size: 0.8rem;
  color: #666;
  opacity: 0.8;
}

.menu-divider {
  height: 1px;
  background: #e1e5e9;
  margin: 10px 20px;
}

.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: -1;
}

/* 动画效果 */
.tools-menu-enter-active,
.tools-menu-leave-active {
  transition: all 0.3s ease;
}

.tools-menu-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.tools-menu-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tools-navigation {
    top: 10px;
    right: 10px;
  }
  
  .tools-menu {
    width: 280px;
  }
  
  .floating-tools-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
}
</style>
